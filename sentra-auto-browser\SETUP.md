# Setup Guide

## Prerequisites

- Node.js 18+ 
- npm or yarn
- One of the following LLM API keys:
  - OpenAI API key
  - Anthropic API key  
  - Google AI API key

## Installation

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd browser-use-nodejs
npm install
```

2. **Install Playwright browser:**
```bash
npm run install-browser
```

3. **Set up environment variables:**
```bash
cp .env.example .env
```

Edit `.env` with your API keys:
```env
# Choose one LLM provider
OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here  
# GOOGLE_API_KEY=your_google_api_key_here

# Optional: Browser settings
BROWSER_HEADLESS=true
BROWSER_WIDTH=1280
BROWSER_HEIGHT=720
```

4. **Build the project:**
```bash
npm run build
```

## Quick Test

Test your setup:
```bash
npm run start test
```

Run a simple example:
```bash
npm run start run "Go to google.com and search for 'hello world'"
```

## Development

Run in development mode:
```bash
npm run dev run "Your task here"
```

Run examples:
```bash
npm run example:simple
npm run example:custom
npm run example:multiple
```

Run tests:
```bash
npm test
```

## Troubleshooting

### Browser Issues
- Make sure Chromium is installed: `npm run install-browser`
- Try running with `--visible` flag to see what's happening
- Check if you have sufficient permissions

### LLM Issues  
- Verify your API key is correct
- Check your API quota/billing
- Try a different model with `--model` flag

### General Issues
- Enable debug logging with `--debug` flag
- Check the logs for specific error messages
- Make sure all dependencies are installed

## API Keys Setup

### OpenAI
1. Go to https://platform.openai.com/api-keys
2. Create a new API key
3. Add to `.env`: `OPENAI_API_KEY=sk-...`

### Anthropic
1. Go to https://console.anthropic.com/
2. Create an API key
3. Add to `.env`: `ANTHROPIC_API_KEY=sk-ant-...`

### Google AI
1. Go to https://aistudio.google.com/app/apikey
2. Create an API key
3. Add to `.env`: `GOOGLE_API_KEY=...`

## Next Steps

- Read the [README.md](README.md) for usage examples
- Check out the [examples/](examples/) directory
- Customize configuration in `.env` file
