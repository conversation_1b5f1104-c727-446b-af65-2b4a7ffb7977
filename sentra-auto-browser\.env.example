# LLM Configuration
# Choose your preferred provider by uncommenting the relevant section

# OpenAI Configuration (Default)
OPENAI_API_KEY=sk-t8zcWN8dFJxaD18REKRrdLzlngOJlmpkzvfomfyLwaYMNcO6
OPENAI_MODEL=gemini-2.5-flash
# Custom OpenAI-compatible API (for proxies/alternatives)
OPENAI_BASE_URL=https://yuanplus.cloud/v1
# Examples for alternative providers:
# OPENAI_BASE_URL=https://yuanplus.cloud/v1
# OPENAI_BASE_URL=https://api.deepseek.com/v1
# OPENAI_BASE_URL=https://api.moonshot.cn/v1

# Anthropic Configuration
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# ANTHROPIC_MODEL=claude-3-5-sonnet-20241022

# Google Configuration
# GOOGLE_API_KEY=your_google_api_key_here
# GOOGLE_MODEL=gemini-1.5-flash

# LLM Settings
LLM_TEMPERATURE=0
LLM_MAX_TOKENS=4000

# Browser Configuration
BROWSER_HEADLESS=false
BROWSER_WIDTH=1280
BROWSER_HEIGHT=720
BROWSER_TIMEOUT=30000

# Custom Browser Paths (optional)
# If not set, will use Playwright's default browser installation
# BROWSER_EXECUTABLE_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe
# BROWSER_EXECUTABLE_PATH=C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe
# BROWSER_USER_DATA_DIR=C:\Users\<USER>\AppData\Local\Google\Chrome\User Data

# Playwright Browser Installation
# Set to false to disable auto-install of browser
BROWSER_AUTO_INSTALL=true

# Agent Configuration
AGENT_MAX_STEPS=50
AGENT_MAX_ACTIONS_PER_STEP=3
AGENT_USE_VISION=true

# Logging
LOG_LEVEL=info
DEBUG=false
