import { logger } from '../utils/logger';
import { DOMService } from '../dom/service';
import { PageStateMonitor } from './page-state-monitor';

/**
 * 🔍 增强DOM检测器 - 基于browser-use架构重构
 * 智能缓存、动态失效、页面感知的元素检测
 */
export class EnhancedDOMDetector {
  private page: any;
  private domService: DOMService;
  private pageStateMonitor: PageStateMonitor;
  private elementCache: Map<string, any> = new Map();
  private cacheTimestamps: Map<string, number> = new Map();
  private lastPageState: any = null;
  private detectionInProgress: boolean = false;
  private cacheValidityDuration: number = 30000; // 30秒缓存有效期

  constructor(page: any, pageStateMonitor: PageStateMonitor) {
    this.page = page;
    this.domService = new DOMService(page);
    this.pageStateMonitor = pageStateMonitor;
    
    // 监听页面状态变化
    this.pageStateMonitor.addChangeListener(this.onPageStateChange.bind(this));
  }

  /**
   * 检测页面元素 - 智能缓存版本
   */
  async detectElements(forceRefresh: boolean = false) {
    try {
      if (this.detectionInProgress) {
        logger.info('🔄 检测正在进行中，等待完成...', 'EnhancedDOMDetector');
        return await this.waitForDetectionComplete();
      }

      this.detectionInProgress = true;
      
      const currentState = this.pageStateMonitor.getCurrentState();
      const cacheKey = this.generateCacheKey(currentState);
      
      // 检查缓存是否有效
      if (!forceRefresh && this.isCacheValid(cacheKey)) {
        logger.info('✅ 使用缓存的DOM检测结果', 'EnhancedDOMDetector');
        this.detectionInProgress = false;
        return this.elementCache.get(cacheKey);
      }
      
      logger.info('🔍 开始新的DOM元素检测...', 'EnhancedDOMDetector');
      
      // 等待页面稳定
      await this.waitForPageStability();
      
      // 执行DOM检测
      const domState = await this.domService.getDOMState();

      // 增强元素信息
      const enhancedElements = await this.enhanceElements(domState.elements);

      // 确保结果包含必要的属性
      const result = {
        elements: enhancedElements,
        url: domState.url || this.page.url(),
        title: domState.title || await this.page.title(),
        screenshot: domState.screenshot || '',
        detectionTime: Date.now(),
        pageState: currentState
      };
      
      // 更新缓存
      this.updateCache(cacheKey, result);
      
      // 添加页面标记
      await this.addPageMarkers(enhancedElements);
      
      logger.info(`✅ DOM检测完成: ${enhancedElements.length}个元素`, 'EnhancedDOMDetector');
      
      this.detectionInProgress = false;
      return result;
      
    } catch (error: any) {
      logger.error(`❌ DOM检测失败: ${error.message}`, error, 'EnhancedDOMDetector');
      this.detectionInProgress = false;
      throw error;
    }
  }

  /**
   * 页面状态变化处理
   */
  async onPageStateChange(oldState: any, newState: any, eventType: string) {
    try {
      logger.info(`🔄 页面状态变化，清理DOM缓存: ${eventType}`, 'EnhancedDOMDetector');
      
      // 清理所有缓存
      this.invalidateAllCache();
      
      // 如果是重大变化，重新检测
      if (newState.hasNewContent) {
        logger.info('🔄 检测到新内容，重新分析页面元素...', 'EnhancedDOMDetector');
        await this.detectElements(true);
      }
      
    } catch (error: any) {
      logger.error(`❌ 处理页面状态变化失败: ${error.message}`, error, 'EnhancedDOMDetector');
    }
  }

  /**
   * 增强元素信息
   */
  async enhanceElements(elements: any[]) {
    try {
      const enhancedElements = [];
      
      for (let i = 0; i < elements.length; i++) {
        const element = elements[i];
        
        try {
          // 获取元素的额外信息
          const enhancement = await this.page.evaluate((index: number) => {
            const elements = document.querySelectorAll('[data-browser-use-index]');
            const el = elements[index];
            
            if (!el) return null;
            
            const rect = el.getBoundingClientRect();
            const style = window.getComputedStyle(el);
            
            return {
              isVisible: rect.width > 0 && rect.height > 0 && style.visibility !== 'hidden' && style.display !== 'none',
              isInViewport: rect.top >= 0 && rect.left >= 0 && rect.bottom <= window.innerHeight && rect.right <= window.innerWidth,
              isClickable: el.tagName.toLowerCase() === 'button' || 
                          el.tagName.toLowerCase() === 'a' || 
                          (el as any).onclick !== null ||
                          el.getAttribute('role') === 'button' ||
                          style.cursor === 'pointer',
              isEditable: el.tagName.toLowerCase() === 'input' || 
                         el.tagName.toLowerCase() === 'textarea' || 
                         (el as any).contentEditable === 'true',
              hasText: el.textContent && el.textContent.trim().length > 0,
              backgroundColor: style.backgroundColor,
              color: style.color,
              fontSize: style.fontSize,
              zIndex: style.zIndex
            };
          }, i);
          
          if (enhancement) {
            enhancedElements.push({
              ...element,
              ...enhancement,
              priority: this.calculateElementPriority(element, enhancement)
            });
          } else {
            enhancedElements.push(element);
          }
          
        } catch (error: any) {
          logger.warn(`⚠️ 增强元素 ${i} 失败: ${error.message}`, 'EnhancedDOMDetector');
          enhancedElements.push(element);
        }
      }
      
      return enhancedElements;
      
    } catch (error: any) {
      logger.error(`❌ 增强元素信息失败: ${error.message}`, error, 'EnhancedDOMDetector');
      return elements;
    }
  }

  /**
   * 计算元素优先级
   */
  calculateElementPriority(element: any, enhancement: any) {
    let priority = 0;
    
    // 可见性
    if (enhancement.isVisible) priority += 10;
    if (enhancement.isInViewport) priority += 15;
    
    // 交互性
    if (enhancement.isClickable) priority += 20;
    if (enhancement.isEditable) priority += 25;
    
    // 内容
    if (enhancement.hasText) priority += 5;
    
    // 元素类型
    const tag = element.tag?.toLowerCase() || '';
    if (tag === 'button') priority += 15;
    else if (tag === 'input') priority += 12;
    else if (tag === 'a') priority += 10;
    else if (tag === 'select') priority += 8;
    
    // 特殊属性
    if (element.attributes?.type === 'submit') priority += 10;
    if (element.attributes?.role === 'button') priority += 8;
    
    return priority;
  }

  /**
   * 添加页面标记 - 增强版本，确保标记持久性
   */
  async addPageMarkers(elements: any[]) {
    try {
      await this.page.evaluate((elementsData: any[]) => {
        // 清除旧标记
        const oldMarkers = document.querySelectorAll('.browser-use-marker');
        oldMarkers.forEach(marker => marker.remove());

        // 重新添加data-browser-use-index属性到所有交互元素
        const interactiveSelectors = [
          'button', 'a[href]', 'input', 'textarea', 'select',
          '[onclick]', '[role="button"]', '[role="link"]',
          '[tabindex]:not([tabindex="-1"])'
        ];

        const allInteractiveElements = document.querySelectorAll(interactiveSelectors.join(','));
        allInteractiveElements.forEach((element, index) => {
          // 强制设置属性，确保不会丢失
          element.setAttribute('data-browser-use-index', index.toString());
          element.setAttribute('data-browser-use-id', `element_${index}`);

          // 添加持久性标记，防止动态内容清除
          if (!element.hasAttribute('data-browser-use-persistent')) {
            element.setAttribute('data-browser-use-persistent', 'true');

            // 监听属性变化，自动恢复
            const observer = new MutationObserver((mutations) => {
              mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' &&
                    (mutation.attributeName === 'data-browser-use-index' ||
                     mutation.attributeName === 'data-browser-use-id')) {
                  // 属性被删除时自动恢复
                  if (!element.hasAttribute('data-browser-use-index')) {
                    element.setAttribute('data-browser-use-index', index.toString());
                  }
                  if (!element.hasAttribute('data-browser-use-id')) {
                    element.setAttribute('data-browser-use-id', `element_${index}`);
                  }
                }
              });
            });

            observer.observe(element, {
              attributes: true,
              attributeFilter: ['data-browser-use-index', 'data-browser-use-id']
            });
          }
        });

        // 添加可视化标记
        elementsData.forEach((elementData, index) => {
          try {
            const element = document.querySelector(`[data-browser-use-index="${index}"]`);

            if (!element) return;

            const rect = element.getBoundingClientRect();
            if (rect.width === 0 || rect.height === 0) return;

            const marker = document.createElement('div');
            marker.className = 'browser-use-marker';
            marker.textContent = index.toString();

            // 根据元素类型设置颜色
            let color = '#007bff'; // 默认蓝色
            if (elementData.isClickable) color = '#28a745'; // 绿色
            if (elementData.isEditable) color = '#ffc107'; // 黄色
            if (elementData.priority > 30) color = '#dc3545'; // 红色（高优先级）

            marker.style.cssText = `
              position: fixed;
              top: ${rect.top + window.scrollY - 2}px;
              left: ${rect.left + window.scrollX - 2}px;
              background: ${color};
              color: white;
              padding: 2px 6px;
              border-radius: 3px;
              font-size: 12px;
              font-weight: bold;
              z-index: 10000;
              pointer-events: none;
              font-family: monospace;
              box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            `;

            document.body.appendChild(marker);
          } catch (error) {
            console.warn('添加标记失败:', error);
          }
        });
      }, elements);

      logger.info(`🎨 已添加 ${elements.length} 个页面标记（增强持久性）`, 'EnhancedDOMDetector');

    } catch (error: any) {
      logger.warn(`⚠️ 添加页面标记失败: ${error.message}`, 'EnhancedDOMDetector');
    }
  }

  /**
   * 等待页面稳定
   */
  async waitForPageStability() {
    try {
      await this.page.waitForLoadState('domcontentloaded', { timeout: 5000 });
      await this.page.waitForTimeout(1000); // 额外等待动态内容
    } catch (error: any) {
      logger.warn(`⚠️ 等待页面稳定超时: ${error.message}`, 'EnhancedDOMDetector');
    }
  }

  /**
   * 生成缓存键
   */
  generateCacheKey(pageState: any) {
    return `${pageState.url}_${pageState.domHash}_${pageState.timestamp}`;
  }

  /**
   * 检查缓存是否有效
   */
  isCacheValid(cacheKey: string) {
    if (!this.elementCache.has(cacheKey)) return false;
    
    const timestamp = this.cacheTimestamps.get(cacheKey);
    if (!timestamp) return false;
    
    return (Date.now() - timestamp) < this.cacheValidityDuration;
  }

  /**
   * 更新缓存
   */
  updateCache(cacheKey: string, result: any) {
    this.elementCache.set(cacheKey, result);
    this.cacheTimestamps.set(cacheKey, Date.now());
    
    // 清理过期缓存
    this.cleanupExpiredCache();
  }

  /**
   * 清理过期缓存
   */
  cleanupExpiredCache() {
    const now = Date.now();
    
    for (const [key, timestamp] of this.cacheTimestamps.entries()) {
      if (now - timestamp > this.cacheValidityDuration) {
        this.elementCache.delete(key);
        this.cacheTimestamps.delete(key);
      }
    }
  }

  /**
   * 失效所有缓存
   */
  invalidateAllCache() {
    this.elementCache.clear();
    this.cacheTimestamps.clear();
    logger.info('🗑️ 已清理所有DOM缓存', 'EnhancedDOMDetector');
  }

  /**
   * 等待检测完成
   */
  async waitForDetectionComplete() {
    while (this.detectionInProgress) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // 返回最新的缓存结果
    const currentState = this.pageStateMonitor.getCurrentState();
    const cacheKey = this.generateCacheKey(currentState);
    return this.elementCache.get(cacheKey);
  }
}
