OPENAI_API_KEY=
ANTHROPIC_API_KEY=
AZURE_OPENAI_ENDPOINT=
AZURE_OPENAI_KEY=
GOOGLE_API_KEY=
DEEPSEEK_API_KEY=
GROK_API_KEY=
NOVITA_API_KEY=

# Set to false to disable anonymized telemetry
ANONYMIZED_TELEMETRY=true

# LogLevel: Set to debug to enable verbose logging, set to result to get results only. Available: result | debug | info
BROWSER_USE_LOGGING_LEVEL=info

# Calculate costs: (beta) Add cost calculations to tokens. Available: true | false
BROWSER_USE_CALCULATE_COST=false

# set this to true to optimize browser-use's chrome for running inside docker
IN_DOCKER=false
