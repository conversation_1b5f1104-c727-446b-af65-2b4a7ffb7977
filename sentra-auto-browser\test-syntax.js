const { BrowserSession, Config } = require('./dist');

async function testSyntax() {
  try {
    console.log('🔍 Testing DOM Script Syntax\n');

    // Create browser session
    const browserProfile = { ...Config.getBrowserProfile(), headless: false };
    const browserSession = new BrowserSession(browserProfile);
    await browserSession.start();

    // Navigate to a simple page
    await browserSession.navigate('about:blank');

    console.log('📋 Testing Script Syntax...');

    // Read the script
    const fs = require('fs');
    const path = require('path');
    const scriptPath = path.join(__dirname, 'src', 'dom', 'buildDomTree.js');
    const domScript = fs.readFileSync(scriptPath, 'utf8');

    // Test syntax by trying to parse it
    const syntaxResult = await browserSession.page.evaluate((script) => {
      try {
        // Try to parse the script as a function
        new Function('return ' + script);
        return { success: true, message: 'Syntax is valid' };
      } catch (error) {
        return { 
          success: false, 
          error: error.message, 
          line: error.lineNumber || 'unknown',
          column: error.columnNumber || 'unknown'
        };
      }
    }, domScript);

    console.log('📊 Syntax Test Results:');
    console.log(`- Success: ${syntaxResult.success}`);
    if (syntaxResult.error) {
      console.log(`- Error: ${syntaxResult.error}`);
      console.log(`- Line: ${syntaxResult.line}`);
      console.log(`- Column: ${syntaxResult.column}`);
    }

    // If syntax is valid, try to execute it
    if (syntaxResult.success) {
      console.log('\n📋 Testing Script Execution...');
      
      const execResult = await browserSession.page.evaluate((script) => {
        try {
          const fn = eval(script);
          const result = fn({
            doHighlightElements: false,
            focusHighlightIndex: -1,
            viewportExpansion: 0,
            debugMode: false,
          });
          
          return {
            success: true,
            hasRootId: !!result.rootId,
            hasMap: !!result.map,
            mapSize: result.map ? Object.keys(result.map).length : 0
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
            stack: error.stack
          };
        }
      }, domScript);

      console.log('📊 Execution Test Results:');
      console.log(`- Success: ${execResult.success}`);
      if (execResult.success) {
        console.log(`- Has rootId: ${execResult.hasRootId}`);
        console.log(`- Has map: ${execResult.hasMap}`);
        console.log(`- Map size: ${execResult.mapSize}`);
      } else {
        console.log(`- Error: ${execResult.error}`);
        if (execResult.stack) {
          console.log(`- Stack: ${execResult.stack.substring(0, 300)}...`);
        }
      }
    }

    await browserSession.close();
    
    console.log('\n🎉 Syntax test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

testSyntax().catch(console.error);
