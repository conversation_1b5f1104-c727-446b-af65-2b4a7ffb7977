/**
 * 🚀 增强浏览器功能示例
 * 展示如何使用新的智能功能：页面状态监控、智能标签页管理、动态DOM检测、增强按键处理
 */

const { BrowserSession, Config } = require('./dist');

async function demonstrateEnhancedFeatures() {
  console.log('🚀 启动增强浏览器功能演示...\n');

  try {
    // 1. 创建浏览器会话
    console.log('📋 步骤 1: 创建浏览器会话');
    const browserProfile = { 
      ...Config.getBrowserProfile(), 
      headless: false,
      viewport: { width: 1280, height: 720 }
    };
    const browserSession = new BrowserSession(browserProfile);
    await browserSession.start();
    console.log('✅ 浏览器会话已启动\n');

    // 2. 启用增强模式
    console.log('📋 步骤 2: 启用增强模式');
    await browserSession.enableEnhancedMode();
    console.log('✅ 增强模式已启用 - 所有智能功能现已激活\n');

    // 3. 导航到测试页面
    console.log('📋 步骤 3: 导航到测试页面');
    await browserSession.navigate('https://www.bilibili.com');
    await new Promise(resolve => setTimeout(resolve, 3000));
    console.log('✅ 页面加载完成\n');

    // 4. 演示增强DOM检测
    console.log('📋 步骤 4: 演示增强DOM检测');
    const domState = await browserSession.getEnhancedDOMState();
    console.log(`🔍 检测到 ${domState.elements.length} 个元素`);
    console.log(`📊 页面URL: ${domState.url}`);
    console.log(`📄 页面标题: ${domState.title}`);
    console.log('✅ DOM检测完成，页面已标记\n');

    // 5. 演示智能按键处理 - Enter键优化
    console.log('📋 步骤 5: 演示增强按键处理');
    
    // 查找搜索框
    const searchElements = domState.elements.filter(el => 
      el.tag === 'input' && 
      (el.attributes?.type === 'search' || 
       el.attributes?.placeholder?.includes('搜索') ||
       el.attributes?.placeholder?.includes('search'))
    );

    if (searchElements.length > 0) {
      console.log(`🔍 找到搜索框，索引: ${searchElements[0].index}`);
      
      // 点击搜索框
      await browserSession.click(searchElements[0].index);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 输入搜索内容
      await browserSession.type(searchElements[0].index, 'Node.js 教程');
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 使用增强的Enter键处理
      console.log('⌨️ 使用增强Enter键处理...');
      const navigationDetected = await browserSession.pressKey('Enter', [], {
        waitForNavigation: true,
        expectFormSubmit: false, // 搜索通常不是表单提交
        retryCount: 3
      });
      
      if (navigationDetected) {
        console.log('✅ Enter键触发了页面导航');
      } else {
        console.log('ℹ️ Enter键未触发导航，可能是AJAX搜索');
      }
    } else {
      console.log('⚠️ 未找到搜索框，跳过搜索演示');
    }
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    console.log('✅ 按键处理演示完成\n');

    // 6. 演示多标签页管理
    console.log('📋 步骤 6: 演示智能标签页管理');
    
    // 创建新标签页
    await browserSession.newTab('https://github.com');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    await browserSession.newTab('https://stackoverflow.com');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 获取所有标签页信息
    const allTabs = browserSession.getAllTabs ? browserSession.getAllTabs() : [];
    console.log(`📊 当前有 ${allTabs.length} 个标签页`);
    
    // 智能切换到最佳标签页
    console.log('🎯 执行智能标签页切换...');
    const bestTab = await browserSession.smartSwitchTab({
      preferredDomain: 'github.com',
      avoidErrors: true,
      preferRecent: true
    });
    
    if (bestTab) {
      console.log('✅ 智能切换到GitHub标签页');
      
      // 重新检测新页面的元素
      const newDomState = await browserSession.getEnhancedDOMState(true);
      console.log(`🔍 新页面检测到 ${newDomState.elements.length} 个元素`);
    } else {
      console.log('⚠️ 未找到合适的标签页');
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ 标签页管理演示完成\n');

    // 7. 演示页面状态监控
    console.log('📋 步骤 7: 演示页面状态监控');
    
    // 获取当前页面状态
    const pageState = browserSession.getCurrentPageState ? browserSession.getCurrentPageState() : null;
    if (pageState) {
      console.log(`🌐 当前URL: ${pageState.url}`);
      console.log(`📄 页面标题: ${pageState.title}`);
      console.log(`📊 元素数量: ${pageState.elementCount}`);
      console.log(`🔗 交互元素: ${pageState.interactiveElementCount}`);
      console.log(`⏰ 最后更新: ${new Date(pageState.timestamp).toLocaleTimeString()}`);
    }
    
    // 导航到新页面触发状态监控
    console.log('🔄 导航到新页面以触发状态监控...');
    await browserSession.navigate('https://www.npmjs.com');
    await new Promise(resolve => setTimeout(resolve, 4000));
    
    const newPageState = browserSession.getCurrentPageState ? browserSession.getCurrentPageState() : null;
    if (newPageState) {
      console.log(`✅ 页面状态已更新:`);
      console.log(`🌐 新URL: ${newPageState.url}`);
      console.log(`📄 新标题: ${newPageState.title}`);
      console.log(`🔄 内容变化: ${newPageState.hasNewContent ? '是' : '否'}`);
    }
    
    console.log('✅ 页面状态监控演示完成\n');

    // 8. 获取增强模式统计
    console.log('📋 步骤 8: 查看增强模式统计');
    const stats = browserSession.getEnhancedModeStats();
    if (stats) {
      console.log('📊 增强模式统计:');
      console.log(`   总操作数: ${stats.totalOperations}`);
      console.log(`   成功操作: ${stats.successfulOperations}`);
      console.log(`   失败操作: ${stats.failedOperations}`);
      console.log(`   页面导航: ${stats.pageNavigations}`);
      console.log(`   标签切换: ${stats.tabSwitches}`);
      console.log(`   成功率: ${stats.totalOperations > 0 ? (stats.successfulOperations / stats.totalOperations * 100).toFixed(1) : 0}%`);
    }
    console.log('✅ 统计信息显示完成\n');

    // 9. 等待用户观察
    console.log('📋 步骤 9: 等待观察结果');
    console.log('🔍 请观察浏览器中的页面标记和功能演示');
    console.log('⏳ 等待 10 秒后自动关闭...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    // 10. 清理资源
    console.log('📋 步骤 10: 清理资源');
    await browserSession.close();
    console.log('✅ 浏览器会话已关闭\n');

    console.log('🎉 增强浏览器功能演示完成！');
    console.log('\n📋 演示的功能包括:');
    console.log('   ✅ 智能页面状态监控 - 实时检测页面变化');
    console.log('   ✅ 智能标签页管理 - 自动评分和切换');
    console.log('   ✅ 增强DOM检测 - 智能缓存和动态标记');
    console.log('   ✅ 优化按键处理 - 特别是Enter键的智能处理');
    console.log('   ✅ 统一协调控制 - 所有组件协同工作');

  } catch (error) {
    console.error('❌ 演示过程中发生错误:', error.message);
    console.error('🔧 请检查:');
    console.error('   1. 浏览器是否正确安装');
    console.error('   2. 网络连接是否正常');
    console.error('   3. 项目依赖是否完整');
  }
}

// 运行演示
if (require.main === module) {
  demonstrateEnhancedFeatures().catch(error => {
    console.error('启动演示失败:', error);
    process.exit(1);
  });
}

module.exports = { demonstrateEnhancedFeatures };
