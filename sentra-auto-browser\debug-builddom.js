const { BrowserSession } = require('./dist/browser/session');

async function debugBuildDom() {
  console.log('🔍 调试buildDomTree.js执行...');
  
  const session = new BrowserSession();
  
  try {
    await session.start();
    await session.navigate('https://www.baidu.com');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 直接在页面中执行buildDomTree.js并查看返回值
    const result = await session.page.evaluate(() => {
      // 简单的DOM检测脚本
      const elements = [];
      let index = 0;
      
      // 查找所有可交互元素
      const selectors = [
        'input:not([type="hidden"]):not([disabled])',
        'button:not([disabled])',
        'a[href]:not([href="#"]):not([href=""])',
        'textarea:not([disabled])',
        'select:not([disabled])'
      ];
      
      selectors.forEach(selector => {
        const found = document.querySelectorAll(selector);
        found.forEach(element => {
          const rect = element.getBoundingClientRect();
          if (rect.width > 0 && rect.height > 0) {
            element.setAttribute('data-browser-use-index', index.toString());
            
            elements.push({
              index: index,
              tag: element.tagName.toLowerCase(),
              text: element.textContent?.trim() || '',
              attributes: {
                id: element.id || '',
                class: element.className || '',
                name: element.getAttribute('name') || '',
                type: element.getAttribute('type') || '',
                placeholder: element.getAttribute('placeholder') || ''
              },
              xpath: '',
              isClickable: true,
              isVisible: true
            });
            
            index++;
          }
        });
      });
      
      return {
        elements: elements,
        totalFound: elements.length,
        debug: {
          inputCount: document.querySelectorAll('input').length,
          buttonCount: document.querySelectorAll('button').length,
          linkCount: document.querySelectorAll('a').length
        }
      };
    });
    
    console.log('✅ 直接DOM检测结果:');
    console.log(`  - 找到 ${result.totalFound} 个可交互元素`);
    console.log(`  - 调试信息:`, result.debug);
    
    if (result.elements.length > 0) {
      console.log('前5个元素:');
      result.elements.slice(0, 5).forEach((el, i) => {
        console.log(`  ${i + 1}. [${el.index}] ${el.tag} - "${el.text.substring(0, 50)}" (${el.attributes.name || el.attributes.id || 'no-name'})`);
      });
    }
    
  } catch (error) {
    console.error('❌ 调试失败:', error.message);
  } finally {
    await session.close();
  }
}

debugBuildDom().catch(console.error);
