# Sentra Auto Browser - 快速开始

## 安装步骤

1. **清理并安装依赖**
   ```bash
   # 删除旧的依赖（如果存在）
   rmdir /s /q node_modules
   del package-lock.json

   # 安装依赖
   npm install --legacy-peer-deps
   ```

2. **构建项目**
   ```bash
   npm run build
   ```

3. **安装浏览器**
   ```bash
   # 自动安装（推荐）
   install-browser.bat

   # 或手动安装
   npx playwright install chromium
   ```

4. **配置API密钥**
   - 复制 `.env.example` 到 `.env`
   - 在 `.env` 文件中配置您的API密钥

## 配置说明

### API配置选项

#### OpenAI（默认推荐）
```env
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o
OPENAI_BASE_URL=https://api.openai.com/v1
```

#### 使用代理服务（如yuanplus.cloud）
```env
OPENAI_API_KEY=your_proxy_api_key_here
OPENAI_MODEL=gpt-4o
OPENAI_BASE_URL=https://yuanplus.cloud/v1
```

#### Google Gemini
```env
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_MODEL=gemini-1.5-flash
```

### 浏览器配置

#### 使用默认浏览器（推荐）
```env
BROWSER_AUTO_INSTALL=true
# 其他配置保持默认
```

#### 使用自定义浏览器路径
```env
BROWSER_EXECUTABLE_PATH=C:\Program Files\Google\Chrome\Application\chrome.exe
BROWSER_AUTO_INSTALL=false
```

## 使用方法

### 基本命令

```bash
# 查看帮助
node dist/cli/index.js --help

# 查看配置
node dist/cli/index.js config

# 测试连接
node dist/cli/index.js test

# 运行任务（使用默认配置）
node dist/cli/index.js run "访问百度首页"

# 指定提供商和模型
node dist/cli/index.js run "访问百度首页" --provider openai --model gpt-4o
```

### 使用批处理文件（Windows）

```bash
# 运行任务
run.bat "访问百度搜索Node.js" --provider openai --model gpt-4o
```

## 常见任务示例

```bash
# 访问网站
node dist/cli/index.js run "访问百度首页"

# 搜索内容
node dist/cli/index.js run "访问Google搜索JavaScript"

# 复杂任务
node dist/cli/index.js run "访问淘宝，搜索手机，查看第一个商品"

# 使用代理API
node dist/cli/index.js run "访问GitHub" --provider openai --model gpt-4o
```

## 故障排除

如果遇到问题：

1. **模块找不到错误**
   ```bash
   npm install --legacy-peer-deps --force
   npm run build
   ```

2. **TypeScript编译错误**
   ```bash
   npm install -g typescript@4.9.5
   npm run build
   ```

3. **API错误**
   - 检查 `.env` 文件中的API密钥
   - 确保API密钥有效且有足够配额

## 项目结构

```
sentra-auto-browser/
├── src/                 # 源代码
├── dist/                # 编译后的代码
├── .env                 # 环境配置
├── package.json         # 项目配置
├── tsconfig.json        # TypeScript配置
└── run.bat             # Windows运行脚本
```

## 注意事项

- 需要Node.js 18+
- 首次运行会自动下载浏览器
- 建议使用可视化模式观察执行过程
- 符号显示已优化为ASCII兼容格式
