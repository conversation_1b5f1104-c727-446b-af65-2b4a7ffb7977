const { BrowserSession } = require('./dist/browser/session');

async function testEnterFix() {
  console.log('🧪 测试Enter键修复...\n');
  
  const browserSession = new BrowserSession({
    headless: false,
    viewport: { width: 1280, height: 720 }
  });

  try {
    // 启动浏览器并启用增强模式
    await browserSession.start();
    await browserSession.enableEnhancedMode();
    console.log('✅ 浏览器和增强模式已启动\n');

    // 导航到B站
    await browserSession.navigate('https://www.bilibili.com');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 获取DOM状态
    const domState = await browserSession.getEnhancedDOMState();
    console.log(`✅ 检测到 ${domState.elements.length} 个元素`);
    console.log(`📍 当前页面: ${domState.url}\n`);

    // 查找搜索框
    const searchElements = domState.elements.filter(el => 
      el.tag === 'input' && 
      el.attributes.class && 
      el.attributes.class.includes('search')
    );
    
    console.log(`🔍 找到 ${searchElements.length} 个搜索框`);
    
    if (searchElements.length > 0) {
      const searchBox = searchElements[0];
      console.log(`🎯 使用搜索框: index ${searchBox.highlightIndex}`);
      console.log(`📝 class: "${searchBox.attributes.class}"`);
      console.log(`📝 placeholder: "${searchBox.attributes.placeholder}"`);
      
      // 点击搜索框
      console.log('\n👆 点击搜索框...');
      await browserSession.click(searchBox.highlightIndex);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 输入搜索内容
      console.log('⌨️ 输入搜索内容...');
      await browserSession.type(searchBox.highlightIndex, '搞笑视频');
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 测试上下文分析
      console.log('\n🔍 测试Enter键上下文分析...');
      
      // 使用增强Enter键 - 强制提交
      console.log('🚀 使用增强Enter键（强制提交）...');
      const result = await browserSession.pressKey('Enter', [], {
        waitForNavigation: true,
        expectFormSubmit: true  // 强制期望提交
      });
      
      console.log(`✅ Enter键结果: ${result}`);

      // 等待标签页切换和页面加载完成
      await new Promise(resolve => setTimeout(resolve, 6000));
      const newState = await browserSession.getEnhancedDOMState(true); // 强制刷新
      console.log(`📍 新页面: ${newState.url}`);
      
      if (newState.url !== domState.url) {
        console.log('🎉 页面导航成功！');
      } else if (newState.url.includes('search') || newState.url.includes('搞笑视频')) {
        console.log('🎉 搜索URL检测成功！');
      } else {
        console.log('⚠️ 页面未跳转，检查内容变化...');
        
        // 检查页面内容是否有搜索结果
        const hasSearchResults = newState.elements.some(el => 
          el.text && el.text.includes('搞笑视频')
        );
        
        if (hasSearchResults) {
          console.log('✅ 检测到搜索结果内容！');
        } else {
          console.log('❌ 未检测到搜索结果');
        }
      }
      
    } else {
      console.log('❌ 未找到搜索框');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await browserSession.close();
    console.log('✅ 测试完成');
  }
}

testEnterFix().catch(console.error);
