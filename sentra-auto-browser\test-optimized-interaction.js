const { BrowserSession } = require('./dist/browser/session');
const { DOMService } = require('./dist/dom/service');

async function testOptimizedInteraction() {
  console.log('🚀 开始测试优化后的交互系统...');
  
  const session = new BrowserSession();
  
  try {
    // 启动浏览器
    await session.start();
    console.log('✅ 浏览器启动成功');
    
    // 导航到测试页面
    await session.navigate('https://www.baidu.com');
    console.log('✅ 导航到百度首页');
    
    // 等待页面加载
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 测试优化的DOM检测
    console.log('🔍 开始DOM检测...');
    const startTime = Date.now();
    
    const domState = await session.getEnhancedDOMState();
    
    const endTime = Date.now();
    console.log(`✅ DOM检测完成: ${domState.elements.length}个元素, 耗时: ${endTime - startTime}ms`);
    
    // 显示前5个元素
    console.log('前5个检测到的元素:');
    domState.elements.slice(0, 5).forEach((element, i) => {
      console.log(`  ${i + 1}. [${element.index}] ${element.tag} - "${element.text.substring(0, 50)}"`);
    });
    
    // 测试点击操作
    if (domState.elements.length > 0) {
      const searchInput = domState.elements.find(el => 
        el.tag === 'input' && 
        (el.attributes.name === 'wd' || el.attributes.id === 'kw')
      );
      
      if (searchInput) {
        console.log(`🎯 找到搜索框，index: ${searchInput.index}`);
        
        // 测试点击
        console.log('测试点击搜索框...');
        await session.click(searchInput.index);
        console.log('✅ 点击成功');
        
        // 测试输入
        console.log('测试输入文本...');
        await session.type(searchInput.index, 'Hello World');
        console.log('✅ 输入成功');
        
        // 测试按键
        console.log('测试Enter键...');
        await session.pressKey('Enter');
        console.log('✅ 按键成功');
        
        // 等待页面响应
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 再次检测DOM状态
        console.log('🔍 检测搜索后的DOM状态...');
        const newStartTime = Date.now();
        const newDomState = await session.getEnhancedDOMState();
        const newEndTime = Date.now();
        
        console.log(`✅ 新DOM检测完成: ${newDomState.elements.length}个元素, 耗时: ${newEndTime - newStartTime}ms`);
        console.log(`📊 性能对比: 首次${endTime - startTime}ms vs 二次${newEndTime - newStartTime}ms`);
        
      } else {
        console.log('❌ 未找到搜索框');
      }
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
  } finally {
    // 关闭浏览器
    await session.close();
    console.log('✅ 浏览器已关闭');
  }
}

// 运行测试
testOptimizedInteraction().catch(console.error);
