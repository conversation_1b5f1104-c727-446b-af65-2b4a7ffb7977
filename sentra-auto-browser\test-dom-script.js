const { BrowserSession, Config } = require('./dist');

async function testDOMScript() {
  try {
    console.log('🔍 Testing DOM Script Directly\n');

    // Create browser session
    const browserProfile = { ...Config.getBrowserProfile(), headless: false };
    const browserSession = new BrowserSession(browserProfile);
    await browserSession.start();

    // Navigate to a simple page
    await browserSession.navigate('https://www.baidu.com');
    await new Promise(resolve => setTimeout(resolve, 3000)); // Wait for page load

    console.log('📋 Testing DOM Script Execution...');

    // Test the DOM script directly
    const result = await browserSession.page.evaluate(() => {
      try {
        // Test if we can access basic DOM elements
        const elements = document.querySelectorAll('*');
        console.log(`Found ${elements.length} total elements`);
        
        // Test basic functionality
        const interactiveElements = [];
        let index = 0;
        
        for (const element of elements) {
          if (element.tagName && 
              (element.tagName.toLowerCase() === 'a' || 
               element.tagName.toLowerCase() === 'button' || 
               element.tagName.toLowerCase() === 'input')) {
            
            const rect = element.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
              element.setAttribute('data-browser-use-index', index.toString());
              
              interactiveElements.push({
                index: index,
                tag: element.tagName.toLowerCase(),
                text: element.textContent?.trim().substring(0, 50) || '',
                visible: true
              });
              
              index++;
              if (index >= 20) break; // Limit for testing
            }
          }
        }
        
        return {
          success: true,
          totalElements: elements.length,
          interactiveElements: interactiveElements,
          message: 'Basic DOM processing successful'
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message,
          message: 'DOM processing failed'
        };
      }
    });

    console.log('📊 Basic DOM Test Results:');
    console.log(`- Success: ${result.success}`);
    console.log(`- Total elements: ${result.totalElements || 'N/A'}`);
    console.log(`- Interactive elements found: ${result.interactiveElements?.length || 0}`);
    
    if (result.interactiveElements && result.interactiveElements.length > 0) {
      console.log('- First few interactive elements:');
      result.interactiveElements.slice(0, 5).forEach(el => {
        console.log(`  ${el.index}: ${el.tag} - "${el.text}"`);
      });
    }

    if (result.error) {
      console.log(`- Error: ${result.error}`);
    }

    // Now test the Python buildDomTree.js script
    console.log('\n📋 Testing Python buildDomTree.js Script...');
    
    const fs = require('fs');
    const path = require('path');
    const scriptPath = path.join(__dirname, 'src', 'dom', 'buildDomTree.js');
    
    if (!fs.existsSync(scriptPath)) {
      console.log('❌ buildDomTree.js not found at:', scriptPath);
      await browserSession.close();
      return;
    }
    
    const domScript = fs.readFileSync(scriptPath, 'utf8');
    console.log(`✅ Script loaded, size: ${domScript.length} characters`);

    const domResult = await browserSession.page.evaluate((script) => {
      try {
        // Execute the function with arguments
        const buildDomTreeFn = eval(`(${script})`);
        const result = buildDomTreeFn({
          doHighlightElements: true,
          focusHighlightIndex: -1,
          viewportExpansion: 0,
          debugMode: false,
        });
        
        return {
          success: true,
          hasRootId: !!result.rootId,
          hasMap: !!result.map,
          mapSize: result.map ? Object.keys(result.map).length : 0,
          hasPerfMetrics: !!result.perfMetrics,
          message: 'Python script executed successfully'
        };
        
      } catch (error) {
        return {
          success: false,
          error: error.message,
          stack: error.stack,
          message: 'Python script execution failed'
        };
      }
    }, domScript);

    console.log('📊 Python Script Test Results:');
    console.log(`- Success: ${domResult.success}`);
    console.log(`- Has rootId: ${domResult.hasRootId || false}`);
    console.log(`- Has map: ${domResult.hasMap || false}`);
    console.log(`- Map size: ${domResult.mapSize || 0}`);
    console.log(`- Has performance metrics: ${domResult.hasPerfMetrics || false}`);
    
    if (domResult.error) {
      console.log(`- Error: ${domResult.error}`);
      if (domResult.stack) {
        console.log(`- Stack: ${domResult.stack.substring(0, 200)}...`);
      }
    }

    await browserSession.close();
    
    console.log('\n🎉 DOM script test completed!');
    
    if (result.success && domResult.success) {
      console.log('✅ Both basic DOM and Python script tests passed!');
    } else {
      console.log('❌ Some tests failed. Check the errors above.');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

testDOMScript().catch(console.error);
