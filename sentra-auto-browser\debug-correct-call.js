const { BrowserSession } = require('./dist/browser/session');
const fs = require('fs');
const path = require('path');

async function debugCorrectCall() {
  console.log('🔍 调试buildDomTree.js的正确调用方式...');
  
  const session = new BrowserSession();
  
  try {
    await session.start();
    await session.navigate('https://www.baidu.com');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 加载buildDomTree.js脚本
    const scriptPath = path.join(__dirname, 'dist/dom/buildDomTree.js');
    const buildDomTreeScript = fs.readFileSync(scriptPath, 'utf8');
    
    console.log('📄 buildDomTree.js脚本前100字符:', buildDomTreeScript.substring(0, 100));
    console.log('📄 buildDomTree.js脚本后100字符:', buildDomTreeScript.substring(buildDomTreeScript.length - 100));
    
    // 测试不同的调用方式
    console.log('\n🧪 测试方式1: 直接evaluate函数');
    const result1 = await session.page.evaluate((script) => {
      try {
        const args = {
          doHighlightElements: true,
          focusHighlightIndex: -1,
          viewportExpansion: 0,
          debugMode: true
        };
        
        // buildDomTree.js是一个箭头函数表达式，需要用eval包装
        const func = eval(script);
        const result = func(args);
        
        return {
          success: true,
          hasResult: !!result,
          resultType: typeof result,
          resultKeys: result ? Object.keys(result) : [],
          mapSize: result && result.map ? Object.keys(result.map).length : 0,
          error: null
        };
      } catch (error) {
        return {
          success: false,
          hasResult: false,
          resultType: 'undefined',
          resultKeys: [],
          mapSize: 0,
          error: error.message
        };
      }
    }, buildDomTreeScript);
    
    console.log('结果1:', result1);
    
    console.log('\n🧪 测试方式2: 使用Function构造器');
    const result2 = await session.page.evaluate((script) => {
      try {
        const args = {
          doHighlightElements: true,
          focusHighlightIndex: -1,
          viewportExpansion: 0,
          debugMode: true
        };
        
        // 使用Function构造器
        const func = new Function('return ' + script)();
        const result = func(args);
        
        return {
          success: true,
          hasResult: !!result,
          resultType: typeof result,
          resultKeys: result ? Object.keys(result) : [],
          mapSize: result && result.map ? Object.keys(result.map).length : 0,
          error: null
        };
      } catch (error) {
        return {
          success: false,
          hasResult: false,
          resultType: 'undefined',
          resultKeys: [],
          mapSize: 0,
          error: error.message
        };
      }
    }, buildDomTreeScript);
    
    console.log('结果2:', result2);
    
    console.log('\n🧪 测试方式3: 直接执行并传参');
    const result3 = await session.page.evaluate((script, args) => {
      try {
        // 直接执行脚本并传参
        const func = eval(`(${script})`);
        const result = func(args);
        
        return {
          success: true,
          hasResult: !!result,
          resultType: typeof result,
          resultKeys: result ? Object.keys(result) : [],
          mapSize: result && result.map ? Object.keys(result.map).length : 0,
          highlightedElements: result && result.map ? 
            Object.values(result.map).filter(node => typeof node.highlightIndex === 'number').length : 0,
          error: null
        };
      } catch (error) {
        return {
          success: false,
          hasResult: false,
          resultType: 'undefined',
          resultKeys: [],
          mapSize: 0,
          highlightedElements: 0,
          error: error.message
        };
      }
    }, buildDomTreeScript, {
      doHighlightElements: true,
      focusHighlightIndex: -1,
      viewportExpansion: 0,
      debugMode: true
    });
    
    console.log('结果3:', result3);
    
    // 如果方式3成功，获取实际的DOM分析结果
    if (result3.success && result3.highlightedElements > 0) {
      console.log('\n✅ 方式3成功！获取实际结果...');
      const actualResult = await session.page.evaluate((script, args) => {
        const func = eval(`(${script})`);
        const result = func(args);
        
        // 提取前5个高亮元素
        const highlightedElements = [];
        for (const [id, node] of Object.entries(result.map)) {
          if (typeof node.highlightIndex === 'number' && highlightedElements.length < 5) {
            highlightedElements.push({
              id,
              highlightIndex: node.highlightIndex,
              tagName: node.tagName,
              text: (node.text || '').substring(0, 50),
              attributes: node.attributes
            });
          }
        }
        
        return {
          totalNodes: Object.keys(result.map).length,
          highlightedElements: highlightedElements,
          perfMetrics: result.perfMetrics
        };
      }, buildDomTreeScript, {
        doHighlightElements: true,
        focusHighlightIndex: -1,
        viewportExpansion: 0,
        debugMode: true
      });
      
      console.log('实际结果:', actualResult);
    }
    
  } catch (error) {
    console.error('❌ 调试失败:', error.message);
  } finally {
    await session.close();
  }
}

debugCorrectCall().catch(console.error);
