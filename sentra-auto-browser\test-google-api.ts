import { config } from 'dotenv';
import { GoogleLLM } from './src/llm/google';

// 加载环境变量
config();

async function testGoogleAPI() {
  console.log('🧪 测试 Google Gemini API 连接\n');

  // 检查 API 密钥
  const apiKey = process.env.GOOGLE_API_KEY;
  if (!apiKey) {
    console.error('❌ 错误: 未找到 GOOGLE_API_KEY 环境变量');
    console.log('\n解决方案:');
    console.log('1. 访问 https://aistudio.google.com/app/apikey');
    console.log('2. 创建 API 密钥');
    console.log('3. 在 .env 文件中设置: GOOGLE_API_KEY=你的密钥');
    process.exit(1);
  }

  console.log(`🔑 API 密钥: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 4)}`);

  // 创建 Google LLM 实例
  const llmConfig = {
    provider: 'google' as const,
    model: 'gemini-1.5-pro',
    apiKey: apiKey,
    temperature: 0,
  };

  console.log(`🤖 模型: ${llmConfig.model}`);
  console.log(`🌡️  温度: ${llmConfig.temperature}\n`);

  try {
    const googleLLM = new GoogleLLM(llmConfig);

    console.log('📤 发送测试请求...');
    
    const testMessages = [
      {
        role: 'system' as const,
        content: 'You are a helpful assistant. Respond briefly and clearly.'
      },
      {
        role: 'user' as const,
        content: 'Hello! Please respond with "API connection successful" if you can understand this message.'
      }
    ];

    const startTime = Date.now();
    const response = await googleLLM.generateResponse(testMessages);
    const duration = Date.now() - startTime;

    console.log('📥 收到响应!\n');
    console.log('='.repeat(50));
    console.log('📋 响应内容:');
    console.log(response.content);
    console.log('='.repeat(50));
    console.log(`⏱️  响应时间: ${duration}ms`);
    console.log(`📊 使用情况: ${JSON.stringify(response.usage, null, 2)}`);

    // 验证响应
    if (response.content.toLowerCase().includes('successful') || 
        response.content.toLowerCase().includes('success')) {
      console.log('\n✅ Google Gemini API 连接成功!');
      console.log('🎉 你现在可以使用 Google Gemini 进行浏览器自动化了!');
    } else {
      console.log('\n⚠️  API 连接成功，但响应内容可能不符合预期');
      console.log('这通常不是问题，API 工作正常');
    }

  } catch (error) {
    console.error('\n❌ API 测试失败:');
    console.error(error instanceof Error ? error.message : String(error));

    if (error instanceof Error) {
      const errorMessage = error.message.toLowerCase();
      
      if (errorMessage.includes('api key') || errorMessage.includes('authentication')) {
        console.log('\n💡 可能的解决方案:');
        console.log('1. 检查 API 密钥是否正确');
        console.log('2. 确保 API 密钥有效且未过期');
        console.log('3. 验证 Google AI API 是否已启用');
      } else if (errorMessage.includes('quota') || errorMessage.includes('limit')) {
        console.log('\n💡 可能的解决方案:');
        console.log('1. 检查 API 配额是否用完');
        console.log('2. 等待配额重置');
        console.log('3. 考虑升级 API 计划');
      } else if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
        console.log('\n💡 可能的解决方案:');
        console.log('1. 检查网络连接');
        console.log('2. 稍后重试');
        console.log('3. 检查防火墙设置');
      } else {
        console.log('\n💡 通用解决方案:');
        console.log('1. 检查 .env 文件配置');
        console.log('2. 验证 API 密钥权限');
        console.log('3. 查看 Google AI Studio 控制台');
      }

      console.log('\n🔍 详细错误信息:');
      console.error(error.stack);
    }

    process.exit(1);
  }
}

// 运行测试
testGoogleAPI();
