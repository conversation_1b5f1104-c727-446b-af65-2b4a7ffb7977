---
title: "Local Setup"
description: "Set up Browser Use development environment locally"
icon: "laptop-code"
---

# Welcome to Browser Use Development!

We're excited to have you join our community of contributors. This guide will help you set up your local development environment quickly and easily.

## Quick Setup

If you're familiar with Python development, here's the quick way to get started:

```bash
git clone https://github.com/browser-use/browser-use
cd browser-use
uv sync --all-extras --dev
# or pip install -U git+https://github.com/browser-use/browser-use.git@main

echo "BROWSER_USE_LOGGING_LEVEL=debug" >> .env
```

## Helper Scripts

We provide several convenient shell scripts in the `bin/` directory to help with common development tasks:

```bash
# Complete setup script - installs uv, creates a venv, and installs dependencies
./bin/setup.sh

# Run all pre-commit hooks (formatting, linting, type checking)
./bin/lint.sh

# Run the core test suite that's executed in CI
./bin/test.sh
```

## Prerequisites

Browser Use requires Python 3.11 or higher. We recommend using [uv](https://docs.astral.sh/uv/) for Python environment management.

## Detailed Setup Instructions

### Clone the Repository

First, clone the Browser Use repository:

```bash
git clone https://github.com/browser-use/browser-use
cd browser-use
```

### Environment Setup

1. Create and activate a virtual environment:

```bash
uv venv --python 3.11
source .venv/bin/activate
```

2. Install dependencies:

```bash
# Install the package in editable mode with all development dependencies
uv sync --all-extras

# Install the default browser
playwright install chromium --with-deps --no-shell
```

## Configuration

Set up your environment variables:

```bash
# Copy the example environment file
cp .env.example .env
```

Or manually create a `.env` file with the API key for the models you want to use set:

```bash .env
OPENAI_API_KEY=...
ANTHROPIC_API_KEY=
AZURE_ENDPOINT=
AZURE_OPENAI_API_KEY=
GOOGLE_API_KEY=
DEEPSEEK_API_KEY=
GROK_API_KEY=
NOVITA_API_KEY=
BROWSER_USE_LOGGING_LEVEL=debug  # Helpful for development
```

<Note>
  See [Supported Models](/customize/supported-models) for available LLM options
  and their specific API key requirements.
</Note>

## Development

After setup, you can:

- Try demos in the example library with `uv run examples/simple.py`
- Run the linter/formatter with `uv run ruff format examples/some/file.py`
- Run tests with `uv run pytest`
- Build the package with `uv build`

### Linting

```bash
# Run the linter on the whole project (must pass for PR to be allowed to merge)
uv run pre-commit run --all-files
# or use our convenience script
./bin/lint.sh

# Install the linter & formatter pre-commit hooks to run automatically
pre-commit install --install-hooks

# Experimental: run the type checker
uv run type
```

### Tests

```bash
# Run all tests that run in CI
./bin/test.sh

# Run specific tests
uv run pytest                                                                         # run everything
uv run pytest tests/test_controller.py                                                # run a specific test file
uv run pytest tests/test_sensitive_data.py tests/test_tab_management.py               # run two test files
uv run pytest tests/test_tab_management.py::TestTabManagement::test_user_changes_tab  # run a single test
```

### Build

```bash
uv build
uv pip install dist/*.whl

# push build to PyPI (automatically run by Github Actions CI)
uv publish
```

## Getting Help

If you run into any issues:

1. Check our [GitHub Issues](https://github.com/browser-use/browser-use/issues)
2. Join our [Discord community](https://link.browser-use.com/discord) for support

<Note>
  We welcome contributions! See our [Contribution
  Guide](/development/contribution-guide) for guidelines on how to help improve
  Browser Use.
</Note>
