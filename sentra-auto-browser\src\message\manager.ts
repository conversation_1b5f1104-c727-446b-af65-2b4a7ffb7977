import { AgentStep, AgentOutput, DOMState } from '../types';
import { logger } from '../utils/logger';

export interface MessageContext {
  task: string;
  stepNumber: number;
  agentHistory: string;
  currentState: string;
  lastResult?: string;
}

export class MessageManager {
  private task: string;
  private maxHistorySteps: number;
  private contextWindow: number;

  constructor(task: string, maxHistorySteps: number = 10, contextWindow: number = 8000) {
    this.task = task;
    this.maxHistorySteps = maxHistorySteps;
    this.contextWindow = contextWindow;
  }

  // Format agent history for LLM context
  formatAgentHistory(history: AgentStep[]): string {
    if (history.length === 0) {
      return '<s>Agent initialized</s>\n';
    }

    let historyText = '<s>Agent initialized</s>\n';
    
    // Only include recent steps to manage context window
    const recentSteps = history.slice(-this.maxHistorySteps);
    
    for (const step of recentSteps) {
      historyText += `\n<step_${step.stepNumber}>:\n`;
      
      if (step.agentOutput) {
        historyText += `Evaluation of Previous Step: ${step.agentOutput.evaluation_previous_goal}\n`;
        historyText += `Memory: ${step.agentOutput.memory}\n`;
        historyText += `Next Goal: ${step.agentOutput.next_goal}\n`;
      }
      
      // Enhanced action results formatting (similar to Python version)
      const actionResults = this.formatActionResults(step);
      historyText += `Action Results:\n${actionResults}`;
      
      historyText += '\n</step_' + step.stepNumber + '>\n';
    }

    return historyText;
  }

  // Format action results with detailed information (similar to Python version)
  private formatActionResults(step: AgentStep): string {
    const action = step.action;
    const result = step.result;

    let actionDescription = '';

    // Format action description
    switch (action.type) {
      case 'navigate':
        actionDescription = `navigate(url="${(action as any).url}")`;
        break;
      case 'click':
        actionDescription = `click(index=${(action as any).index})`;
        break;
      case 'type':
        actionDescription = `type(index=${(action as any).index}, text="${(action as any).text}")`;
        break;
      case 'scroll':
        actionDescription = `scroll(direction="${(action as any).direction}")`;
        break;
      case 'wait':
        actionDescription = `wait(seconds=${(action as any).seconds})`;
        break;
      case 'done':
        actionDescription = `done(message="${(action as any).message}", success=${(action as any).success})`;
        break;
      default:
        actionDescription = `${action.type}()`;
    }

    // Add result status and message
    const status = result.success ? 'SUCCESS' : 'FAILED';
    let resultText = `${actionDescription} - ${status}`;

    if (result.message) {
      resultText += ` - ${result.message}`;
    }

    if (!result.success && result.error) {
      resultText += ` - Error: ${result.error}`;
    }

    return resultText;
  }

  // Format browser state for LLM context
  formatBrowserState(domState: DOMState): string {
    let stateText = `<browser_state>\n`;
    stateText += `Current URL: ${domState.url}\n`;
    stateText += `Page title: ${domState.title}\n\n`;
    stateText += `Interactive Elements:\n`;

    // Limit elements to prevent context overflow
    const maxElements = 50;
    const elements = domState.elements.slice(0, maxElements);

    // Use the same enhanced formatting as BaseLLM
    const formattedElements = this.formatElementsForLLM(elements);
    stateText += formattedElements + '\n';

    if (domState.elements.length > maxElements) {
      stateText += `... and ${domState.elements.length - maxElements} more elements\n`;
    }

    stateText += `</browser_state>`;

    return stateText;
  }

  /**
   * Format DOM elements for LLM consumption (inspired by Python browser_use)
   * Implements smart optimizations to reduce redundancy and improve clarity
   */
  private formatElementsForLLM(elements: any[]): string {
    // Python version's include_attributes equivalent
    const includeAttributes = [
      'title',
      'type',
      'name',
      'role',
      'tabindex',
      'aria-label',
      'placeholder',
      'value',
      'alt',
      'aria-expanded',
      'id', // Added for better element identification
    ];

    const formattedElements = elements.map((el: any, i: number) => {
      // Use element's actual index if available
      const elementIndex = el.index !== undefined ? el.index : i;

      // Get element text content
      const text = (el.text || '').trim();

      // Build attributes object with only relevant attributes
      const attributesToInclude: Record<string, string> = {};
      for (const attr of includeAttributes) {
        if (el.attributes && el.attributes[attr]) {
          attributesToInclude[attr] = el.attributes[attr];
        }
      }

      // Python version's smart optimizations to reduce redundancy

      // 1. If tag == role attribute, don't include role
      if (el.tag === attributesToInclude.role) {
        delete attributesToInclude.role;
      }

      // 2. If aria-label == text content, don't include aria-label
      if (attributesToInclude['aria-label'] &&
          attributesToInclude['aria-label'].trim() === text) {
        delete attributesToInclude['aria-label'];
      }

      // 3. If placeholder == text content, don't include placeholder
      if (attributesToInclude.placeholder &&
          attributesToInclude.placeholder.trim() === text) {
        delete attributesToInclude.placeholder;
      }

      // Format attributes as key='value' (Python style)
      const attributesStr = Object.entries(attributesToInclude)
        .map(([key, value]) => `${key}='${value}'`)
        .join(' ');

      // Build the element description (Python format with improvements)
      let elementDesc = `[${elementIndex}]<${el.tag}`;

      if (attributesStr) {
        elementDesc += ` ${attributesStr}`;
      }

      if (text) {
        // Add space before >text only if there were NO attributes
        if (!attributesStr) {
          elementDesc += ' ';
        }
        elementDesc += `>${text}`;
      } else if (!attributesStr) {
        // Add space before /> only if neither attributes NOR text were added
        elementDesc += ' ';
      }

      elementDesc += ' />'; // Python style ending

      // Add our enhanced type hints for better LLM understanding
      const typeHint = this.getElementTypeHint(el);
      if (typeHint) {
        elementDesc += ` ${typeHint}`;
      }

      return elementDesc;
    });

    return formattedElements.join('\n');
  }

  /**
   * Get type hint for element (our enhancement over Python version)
   */
  private getElementTypeHint(el: any): string {
    const tag = el.tag.toLowerCase();
    const type = el.attributes?.type?.toLowerCase();

    if (tag === 'input') {
      if (type === 'submit' || type === 'button') {
        return '[BUTTON]';
      } else if (type === 'checkbox') {
        return '[CHECKBOX]';
      } else if (type === 'radio') {
        return '[RADIO]';
      } else if (type === 'file') {
        return '[FILE_INPUT]';
      } else {
        return '[INPUT_FIELD]';
      }
    } else if (tag === 'textarea') {
      return '[INPUT_FIELD]';
    } else if (tag === 'button') {
      return '[BUTTON]';
    } else if (tag === 'a') {
      return '[LINK]';
    } else if (tag === 'select') {
      return '[DROPDOWN]';
    } else if (tag === 'img') {
      return '[IMAGE]';
    } else if (tag === 'video') {
      return '[VIDEO]';
    } else if (tag === 'audio') {
      return '[AUDIO]';
    } else if (el.attributes?.role === 'button') {
      return '[BUTTON]';
    } else if (el.attributes?.role === 'link') {
      return '[LINK]';
    } else if (el.attributes?.role === 'textbox') {
      return '[INPUT_FIELD]';
    }

    return '';
  }

  // Create complete message context
  createMessageContext(
    stepNumber: number,
    domState: DOMState,
    history: AgentStep[],
    lastResult?: string
  ): MessageContext {
    const agentHistory = this.formatAgentHistory(history);
    const currentState = this.formatBrowserState(domState);
    
    return {
      task: this.task,
      stepNumber,
      agentHistory,
      currentState,
      lastResult,
    };
  }

  // Estimate token count (rough approximation)
  estimateTokenCount(text: string): number {
    // Rough approximation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  // Trim context if it exceeds window
  trimContext(context: MessageContext): MessageContext {
    const totalText = context.agentHistory + context.currentState + context.task;
    const estimatedTokens = this.estimateTokenCount(totalText);
    
    if (estimatedTokens <= this.contextWindow) {
      return context;
    }

    logger.warn(`Context too large (${estimatedTokens} tokens), trimming...`, 'MessageManager');
    
    // Trim history first
    const historyLines = context.agentHistory.split('\n');
    const trimmedHistory = historyLines.slice(-Math.floor(historyLines.length * 0.7)).join('\n');
    
    return {
      ...context,
      agentHistory: trimmedHistory,
    };
  }

  // Analyze conversation for patterns
  analyzeConversationPatterns(history: AgentStep[]): {
    repeatedActions: number;
    failureRate: number;
    avgStepDuration: number;
    commonErrors: string[];
  } {
    if (history.length === 0) {
      return {
        repeatedActions: 0,
        failureRate: 0,
        avgStepDuration: 0,
        commonErrors: [],
      };
    }

    // Count repeated actions
    const actionCounts = new Map<string, number>();
    const errors = new Map<string, number>();
    let totalDuration = 0;
    let failures = 0;

    for (let i = 0; i < history.length; i++) {
      const step = history[i];
      const actionKey = `${step.action.type}_${(step.action as any).index || ''}`;
      
      actionCounts.set(actionKey, (actionCounts.get(actionKey) || 0) + 1);
      
      if (!step.result.success) {
        failures++;
        if (step.result.error) {
          errors.set(step.result.error, (errors.get(step.result.error) || 0) + 1);
        }
      }

      if (i > 0) {
        const duration = step.timestamp.getTime() - history[i - 1].timestamp.getTime();
        totalDuration += duration;
      }
    }

    const maxRepeated = Math.max(...Array.from(actionCounts.values()));
    const failureRate = failures / history.length;
    const avgStepDuration = history.length > 1 ? totalDuration / (history.length - 1) : 0;
    
    // Get most common errors
    const commonErrors = Array.from(errors.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([error]) => error);

    return {
      repeatedActions: maxRepeated,
      failureRate,
      avgStepDuration,
      commonErrors,
    };
  }

  // Generate context-aware suggestions
  generateSuggestions(history: AgentStep[], domState: DOMState): string[] {
    const patterns = this.analyzeConversationPatterns(history);
    const suggestions: string[] = [];

    if (patterns.repeatedActions > 3) {
      suggestions.push('Consider trying a different approach - repeated actions detected');
    }

    if (patterns.failureRate > 0.5) {
      suggestions.push('High failure rate detected - consider simplifying the approach');
    }

    if (patterns.commonErrors.length > 0) {
      suggestions.push(`Common errors: ${patterns.commonErrors.join(', ')}`);
    }

    // Check for specific page conditions
    if (domState.elements.length === 0) {
      suggestions.push('No interactive elements found - page may still be loading');
    }

    const hasSearchBox = domState.elements.some(el => 
      el.tag === 'input' && (el.text.toLowerCase().includes('search') || el.text.toLowerCase().includes('搜索'))
    );
    
    if (hasSearchBox && !history.some(step => step.action.type === 'type')) {
      suggestions.push('Search box available - consider using it to find content');
    }

    return suggestions;
  }
}
