const { chromium } = require('playwright');
import { BrowserProfile, DOMState } from '../types';
import { logger } from '../utils/logger';
import { DOMService } from '../dom/service';
import { Helpers } from '../utils/helpers';
import { MasterController } from './master-controller';

// 浏览器会话管理类 - 负责浏览器的生命周期管理
export class BrowserSession {
  private browser: any = null;        // 浏览器实例
  private context: any = null;        // 浏览器上下文
  private page: any = null;           // 当前页面
  private domService: DOMService | null = null;  // DOM 服务
  private profile: BrowserProfile;               // 浏览器配置
  private tabs: any[] = [];                      // 标签页列表
  private currentTabIndex: number = 0;           // 当前标签页索引
  private masterController: MasterController | null = null;  // 主控制器
  private enhancedMode: boolean = false;         // 是否启用增强模式

  constructor(profile: BrowserProfile = {}) {
    this.profile = {
      headless: true,
      viewport: { width: 1280, height: 720 },
      timeout: 30000,
      ...profile,
    };
  }

  async start(): Promise<void> {
    try {
      logger.info('正在启动浏览器会话...', 'BrowserSession');

      // Try to launch browser, with auto-install fallback
      try {
        this.browser = await chromium.launch({
          headless: this.profile.headless,
          executablePath: this.profile.executablePath,
          timeout: this.profile.timeout,
          slowMo: this.profile.slowMo,
          devtools: this.profile.devtools,
          args: this.profile.args,
        });
      } catch (error: any) {
        // If browser not found and auto-install is enabled
        if (error.message.includes("Executable doesn't exist") && this.profile.autoInstall !== false) {
          logger.info('浏览器未找到，正在自动安装...', 'BrowserSession');

          try {
            // Install browser using playwright
            const { execSync } = require('child_process');
            execSync('npx playwright install chromium', { stdio: 'inherit' });

            logger.success('浏览器安装完成，重新启动...', 'BrowserSession');

            // Retry launch without custom executable path
            this.browser = await chromium.launch({
              headless: this.profile.headless,
              timeout: this.profile.timeout,
              slowMo: this.profile.slowMo,
              devtools: this.profile.devtools,
              args: this.profile.args,
            });
          } catch (installError) {
            logger.error('浏览器自动安装失败', installError as Error, 'BrowserSession');
            throw new Error(`浏览器启动失败。请手动运行: npx playwright install chromium`);
          }
        } else {
          throw error;
        }
      }

      // Create context
      this.context = await this.browser.newContext({
        viewport: this.profile.viewport,
        userAgent: this.profile.userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        ignoreHTTPSErrors: this.profile.ignoreHTTPSErrors,
        proxy: this.profile.proxy,
        locale: this.profile.locale,
        timezoneId: this.profile.timezone,
        geolocation: this.profile.geolocation,
        permissions: this.profile.permissions,
        extraHTTPHeaders: this.profile.extraHTTPHeaders,
        colorScheme: this.profile.colorScheme,
        reducedMotion: this.profile.reducedMotion,
        forcedColors: this.profile.forcedColors,
      });

      // Create page
      this.page = await this.context.newPage();
      this.tabs = [this.page];

      // Initialize DOM service
      this.domService = new DOMService(this.page);

      // Set up event listeners
      this.setupEventListeners();

      logger.success('浏览器会话启动成功', 'BrowserSession');
    } catch (error) {
      logger.error('Failed to start browser session', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async close(): Promise<void> {
    try {
      logger.info('Closing browser session...', 'BrowserSession');

      // Set a timeout for the entire close operation
      const closePromise = this.performClose();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Browser close timeout')), 10000);
      });

      await Promise.race([closePromise, timeoutPromise]);
      logger.success('Browser session closed successfully', 'BrowserSession');
    } catch (error) {
      logger.error('Error closing browser session', error as Error, 'BrowserSession');
      // Force kill if normal close fails
      try {
        if (this.browser) {
          await this.browser.close();
        }
      } catch (killError) {
        logger.error('Failed to force close browser', killError as Error, 'BrowserSession');
      }
    }
  }

  private async performClose(): Promise<void> {
    // 首先关闭增强模式
    if (this.enhancedMode) {
      try {
        await this.disableEnhancedMode();
      } catch (error) {
        logger.warn('Failed to disable enhanced mode during close', 'BrowserSession');
      }
    }

    if (this.page) {
      try {
        await this.page.close();
      } catch (error) {
        logger.warn('Failed to close page gracefully', 'BrowserSession');
      }
      this.page = null;
    }

    if (this.context) {
      try {
        await this.context.close();
      } catch (error) {
        logger.warn('Failed to close context gracefully', 'BrowserSession');
      }
      this.context = null;
    }

    if (this.browser) {
      try {
        await this.browser.close();
      } catch (error) {
        logger.warn('Failed to close browser gracefully', 'BrowserSession');
      }
      this.browser = null;
    }

    this.domService = null;
  }

  async navigate(url: string): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      logger.info(`Navigating to: ${url}`, 'BrowserSession');
      await this.page.goto(url, { waitUntil: 'domcontentloaded', timeout: this.profile.timeout });
      await this.page.waitForTimeout(1000); // Wait for page to stabilize
      logger.success(`Successfully navigated to: ${url}`, 'BrowserSession');
    } catch (error) {
      logger.error(`Failed to navigate to: ${url}`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async getDOMState(): Promise<DOMState> {
    if (!this.page || !this.domService) {
      throw new Error('Browser session not started');
    }

    try {
      return await this.domService.getDOMState();
    } catch (error) {
      logger.error('Failed to get DOM state', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async click(index: number, xpath?: string, cssSelector?: string, text?: string, attributes?: Record<string, string>): Promise<boolean> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      logger.info(`🎯 开始点击元素 index: ${index}`, 'BrowserSession');

      // 使用buildDomTree.js设置的data-browser-use-index属性
      const selector = `[data-browser-use-index="${index}"]`;

      // 等待元素存在并可见
      await this.page.waitForSelector(selector, { timeout: 10000 });

      // Store current state to detect navigation
      const currentUrl = this.page.url();
      const currentDOMHash = await this.getDOMStructureHash();

      // 执行点击
      await this.page.click(selector);

      logger.info(`✅ 成功点击元素 index: ${index}`, 'BrowserSession');

      // Wait for potential navigation and page load
      const navigationDetected = await this.waitForPotentialNavigation(currentUrl, currentDOMHash);

      if (navigationDetected) {
        logger.info('🔄 Navigation detected after click, page state may have changed', 'BrowserSession');
      }

      return navigationDetected;
    } catch (error) {
      logger.error(`❌ 点击元素失败 index ${index}`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  /**
   * 创建强健的Playwright定位器 - 符合最佳实践
   */
  private async createRobustLocator(index: number, options: { xpath?: string, cssSelector?: string, text?: string, attributes?: Record<string, string> }): Promise<any> {
    logger.info(`🔍 创建强健定位器 index: ${index}`, 'BrowserSession');

    // 获取缓存的元素信息
    const cachedElement = this.domService['elementCache'].get(`index_${index}`);

    // 策略1: 使用用户可见的属性（Playwright最佳实践）
    if (cachedElement) {
      // 优先使用role + name组合（最推荐）
      const role = cachedElement.attributes?.role;
      if (role && cachedElement.text) {
        try {
          const locator = this.page.getByRole(role as any, { name: new RegExp(cachedElement.text.trim(), 'i') });
          if (await locator.count() === 1) {
            logger.info(`✅ 通过role+name定位成功: ${role}[${cachedElement.text}]`, 'BrowserSession');
            return locator;
          }
        } catch (error) {
          logger.debug(`Role+name策略失败: ${error}`, 'BrowserSession');
        }
      }

      // 使用文本内容定位
      if (cachedElement.text && cachedElement.text.trim()) {
        try {
          const locator = this.page.getByText(cachedElement.text.trim());
          if (await locator.count() === 1) {
            logger.info(`✅ 通过文本定位成功: ${cachedElement.text}`, 'BrowserSession');
            return locator;
          }
        } catch (error) {
          logger.debug(`文本策略失败: ${error}`, 'BrowserSession');
        }
      }

      // 使用label定位（表单元素）
      const label = cachedElement.attributes?.['aria-label'] || cachedElement.attributes?.label;
      if (label) {
        try {
          const locator = this.page.getByLabel(label);
          if (await locator.count() === 1) {
            logger.info(`✅ 通过label定位成功: ${label}`, 'BrowserSession');
            return locator;
          }
        } catch (error) {
          logger.debug(`Label策略失败: ${error}`, 'BrowserSession');
        }
      }

      // 使用placeholder定位
      const placeholder = cachedElement.attributes?.placeholder;
      if (placeholder) {
        try {
          const locator = this.page.getByPlaceholder(placeholder);
          if (await locator.count() === 1) {
            logger.info(`✅ 通过placeholder定位成功: ${placeholder}`, 'BrowserSession');
            return locator;
          }
        } catch (error) {
          logger.debug(`Placeholder策略失败: ${error}`, 'BrowserSession');
        }
      }

      // 使用title属性定位
      const title = cachedElement.attributes?.title;
      if (title) {
        try {
          const locator = this.page.getByTitle(title);
          if (await locator.count() === 1) {
            logger.info(`✅ 通过title定位成功: ${title}`, 'BrowserSession');
            return locator;
          }
        } catch (error) {
          logger.debug(`Title策略失败: ${error}`, 'BrowserSession');
        }
      }
    }

    // 策略2: 使用传入的选择器（回退策略）
    if (options.cssSelector) {
      try {
        const locator = this.page.locator(options.cssSelector);
        if (await locator.count() === 1) {
          logger.info(`✅ 通过CSS选择器定位成功: ${options.cssSelector}`, 'BrowserSession');
          return locator;
        }
      } catch (error) {
        logger.debug(`CSS选择器策略失败: ${error}`, 'BrowserSession');
      }
    }

    if (options.xpath) {
      try {
        const locator = this.page.locator(`xpath=${options.xpath}`);
        if (await locator.count() === 1) {
          logger.info(`✅ 通过XPath定位成功: ${options.xpath}`, 'BrowserSession');
          return locator;
        }
      } catch (error) {
        logger.debug(`XPath策略失败: ${error}`, 'BrowserSession');
      }
    }

    // 策略3: 通过data-browser-use-index属性（最后的回退）
    try {
      const locator = this.page.locator(`[data-browser-use-index="${index}"]`);
      if (await locator.count() === 1) {
        logger.info(`✅ 通过data-browser-use-index定位成功: ${index}`, 'BrowserSession');
        return locator;
      }
    } catch (error) {
      logger.debug(`data-browser-use-index策略失败: ${error}`, 'BrowserSession');
    }

    logger.warn(`❌ 所有定位策略都失败，无法找到元素 ${index}`, 'BrowserSession');
    return null;
  }

  /**
   * 执行强健的点击操作 - 使用Playwright自动等待
   */
  private async performRobustClick(locator: any, index: number): Promise<void> {
    logger.info(`🎯 执行强健点击操作 index: ${index}`, 'BrowserSession');

    try {
      // 使用Playwright的自动等待和可操作性检查
      await locator.waitFor({ state: 'visible', timeout: 10000 });
      await locator.waitFor({ state: 'attached', timeout: 5000 });

      // 确保元素在视口中
      await locator.scrollIntoViewIfNeeded();

      // 等待元素稳定（不在动画中）
      await locator.waitFor({ state: 'stable', timeout: 5000 });

      // 执行点击 - Playwright会自动检查可操作性
      await locator.click({
        timeout: 10000,
        force: false, // 不强制点击，让Playwright检查可操作性
        trial: false  // 不是试运行
      });

      logger.info(`✅ 强健点击操作成功 index: ${index}`, 'BrowserSession');

    } catch (error) {
      logger.error(`❌ 强健点击操作失败 index: ${index}`, error as Error, 'BrowserSession');

      // 尝试强制点击作为最后的回退
      try {
        logger.info(`🔄 尝试强制点击作为回退 index: ${index}`, 'BrowserSession');
        await locator.click({ force: true, timeout: 5000 });
        logger.info(`✅ 强制点击成功 index: ${index}`, 'BrowserSession');
      } catch (forceError) {
        logger.error(`❌ 强制点击也失败 index: ${index}`, forceError as Error, 'BrowserSession');
        throw error; // 抛出原始错误
      }
    }
  }

  private async performClickWithRetry(element: any, index: number, maxRetries: number = 3): Promise<void> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Check if element is still attached and visible
        const isVisible = await element.isVisible();
        if (!isVisible) {
          throw new Error('Element is not visible');
        }

        // Try different click strategies
        if (attempt === 1) {
          // Standard click
          await element.click({ timeout: 5000 });
        } else if (attempt === 2) {
          // Force click (ignore covering elements)
          await element.click({ force: true, timeout: 5000 });
        } else {
          // JavaScript click as last resort
          await element.evaluate((el: HTMLElement) => el.click());
        }

        return; // Success
      } catch (error) {
        logger.warn(`Click attempt ${attempt}/${maxRetries} failed for element ${index}: ${error}`, 'BrowserSession');

        if (attempt === maxRetries) {
          throw error;
        }

        // Wait before retry
        await this.page.waitForTimeout(500 * attempt);
      }
    }
  }

  /**
   * 强健的文本输入 - 使用Playwright最佳实践
   */
  async type(index: number, text: string, xpath?: string, cssSelector?: string, attributes?: Record<string, string>): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      logger.info(`⌨️ 开始输入文本到元素 index: ${index}, text: "${text}"`, 'BrowserSession');

      // 使用buildDomTree.js设置的data-browser-use-index属性
      const selector = `[data-browser-use-index="${index}"]`;

      // 等待元素存在并可见
      await this.page.waitForSelector(selector, { timeout: 10000 });

      // Store current state to detect navigation
      const currentUrl = this.page.url();
      const currentDOMHash = await this.getDOMStructureHash();

      // 清除现有内容并输入新文本
      await this.page.fill(selector, text);

      logger.info(`✅ 成功输入文本到元素 index: ${index}`, 'BrowserSession');

      // Wait for potential navigation after typing (e.g., auto-submit forms)
      const navigationDetected = await this.waitForPotentialNavigation(currentUrl, currentDOMHash);

      if (navigationDetected) {
        logger.info('🔄 Navigation detected after typing, page state may have changed', 'BrowserSession');
      }
    } catch (error) {
      logger.error(`❌ 输入文本失败 index ${index}`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  /**
   * 执行强健的文本输入操作
   */
  private async performRobustType(locator: any, text: string, index: number): Promise<void> {
    logger.info(`⌨️ 执行强健文本输入 index: ${index}`, 'BrowserSession');

    try {
      // 等待元素可见和可编辑
      await locator.waitFor({ state: 'visible', timeout: 10000 });
      await locator.waitFor({ state: 'attached', timeout: 5000 });

      // 确保元素在视口中
      await locator.scrollIntoViewIfNeeded();

      // 检查元素类型和属性
      const tagName = await locator.evaluate((el: any) => el.tagName.toLowerCase());
      const elementType = await locator.evaluate((el: any) => el.type || '');
      logger.info(`📝 元素信息: ${tagName}, type: ${elementType}`, 'BrowserSession');

      // 聚焦元素
      await locator.focus();
      await this.page.waitForTimeout(200); // 等待聚焦完成

      // 根据元素类型选择不同的输入策略
      if (tagName === 'textarea' || (tagName === 'input' && elementType !== 'search')) {
        // 对于普通输入框和文本域
        await locator.clear();
        await locator.fill(text);
      } else {
        // 对于搜索框等特殊元素，使用更温和的方法
        await locator.click(); // 确保聚焦
        await locator.selectText(); // 选择所有文本
        await locator.type(text, { delay: 50 }); // 逐字符输入
      }

      // 验证输入是否成功
      try {
        const inputValue = await locator.inputValue();
        logger.info(`📝 输入验证: 期望="${text}", 实际="${inputValue}"`, 'BrowserSession');

        if (inputValue !== text) {
          logger.warn(`⚠️ 输入验证失败，尝试重新输入`, 'BrowserSession');
          // 尝试重新输入
          await locator.clear();
          await locator.type(text, { delay: 100 }); // 更慢的输入
        }
      } catch (validationError) {
        logger.warn(`⚠️ 无法验证输入值，可能是特殊元素类型`, 'BrowserSession');
      }

      logger.info(`✅ 强健文本输入成功 index: ${index}`, 'BrowserSession');

    } catch (error) {
      logger.error(`❌ 强健文本输入失败 index: ${index}`, error as Error, 'BrowserSession');

      // 尝试最基本的输入作为回退
      try {
        logger.info(`🔄 尝试最基本输入作为回退 index: ${index}`, 'BrowserSession');
        await locator.click(); // 确保聚焦
        await this.page.waitForTimeout(300);

        // 使用页面级别的键盘输入
        await this.page.keyboard.press('Control+a'); // 全选
        await this.page.keyboard.type(text, { delay: 100 }); // 逐字符输入

        logger.info(`✅ 最基本输入成功 index: ${index}`, 'BrowserSession');
      } catch (fallbackError) {
        logger.error(`❌ 最基本输入也失败 index: ${index}`, fallbackError as Error, 'BrowserSession');
        throw error; // 抛出原始错误
      }
    }
  }

  private async performTypeWithRetry(element: any, text: string, index: number, maxRetries: number = 3): Promise<void> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Check if element is still attached and visible
        const isVisible = await element.isVisible();
        if (!isVisible) {
          throw new Error('Input element is not visible');
        }

        // Check if element is editable
        const isEditable = await element.isEditable();
        if (!isEditable) {
          throw new Error('Element is not editable');
        }

        // Try different typing strategies
        if (attempt === 1) {
          // Standard fill
          await element.fill(text, { timeout: 5000 });
        } else if (attempt === 2) {
          // Clear and type
          await element.clear();
          await element.type(text, { delay: 50 });
        } else {
          // JavaScript value setting as last resort
          await element.evaluate((el: HTMLInputElement, value: string) => {
            el.value = value;
            el.dispatchEvent(new Event('input', { bubbles: true }));
            el.dispatchEvent(new Event('change', { bubbles: true }));
          }, text);
        }

        return; // Success
      } catch (error) {
        logger.warn(`Type attempt ${attempt}/${maxRetries} failed for element ${index}: ${error}`, 'BrowserSession');

        if (attempt === maxRetries) {
          throw error;
        }

        // Wait before retry
        await this.page.waitForTimeout(300 * attempt);
      }
    }
  }

  async scroll(direction: 'up' | 'down', amount?: number): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      const scrollAmount = amount || 500;
      const scrollDirection = direction === 'down' ? scrollAmount : -scrollAmount;
      
      await this.page.evaluate((delta) => {
        window.scrollBy(0, delta);
      }, scrollDirection);

      logger.info(`Scrolled ${direction} by ${Math.abs(scrollDirection)}px`, 'BrowserSession');
      await this.page.waitForTimeout(500); // Wait for scroll to complete
    } catch (error) {
      logger.error(`Failed to scroll ${direction}`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async wait(seconds: number): Promise<void> {
    logger.info(`Waiting for ${seconds} seconds...`, 'BrowserSession');
    await new Promise(resolve => setTimeout(resolve, seconds * 1000));
  }

  async takeScreenshot(): Promise<string> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      const screenshot = await this.page.screenshot({
        type: 'png',
        fullPage: false
      });
      return screenshot.toString('base64');
    } catch (error) {
      logger.error('Failed to take screenshot', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async getCurrentState(): Promise<DOMState> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      return await this.domService.getDOMState();
    } catch (error) {
      logger.error('Failed to get current state', error as Error, 'BrowserSession');
      throw error;
    }
  }

  getCurrentUrl(): string {
    if (!this.page) {
      throw new Error('Browser session not started');
    }
    return this.page.url();
  }

  async getCurrentTitle(): Promise<string> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }
    return await this.page.title();
  }

  isStarted(): boolean {
    return this.browser !== null && this.context !== null && this.page !== null;
  }

  // Getter methods for enhanced components
  getCurrentPage() {
    return this.page;
  }

  updateCurrentPage(page: any) {
    this.page = page;
    // 同时更新DOM服务的页面引用
    if (this.domService) {
      this.domService.updatePage(page);
    }
  }

  getBrowser() {
    return this.browser;
  }

  getContext() {
    return this.context;
  }

  /**
   * 创建新标签页并导航
   */
  async createNewTab(url: string): Promise<void> {
    if (!this.context) {
      throw new Error('Browser context not available');
    }

    try {
      logger.info(`Creating new tab and navigating to: ${url}`, 'BrowserSession');

      // 创建新页面
      const newPage = await this.context.newPage();

      // 导航到指定URL
      await newPage.goto(url, {
        waitUntil: 'domcontentloaded',
        timeout: 30000
      });

      // 更新标签页列表
      this.tabs = await this.context.pages();

      logger.info(`Created new tab (index: ${this.tabs.length - 1}) and navigated to ${url}`, 'BrowserSession');

    } catch (error) {
      logger.error('Failed to create new tab', error as Error, 'BrowserSession');
      throw error;
    }
  }

  /**
   * 启用增强模式 - 激活所有智能功能
   */
  async enableEnhancedMode(): Promise<void> {
    try {
      if (this.enhancedMode) {
        logger.warn('⚠️ 增强模式已经启用', 'BrowserSession');
        return;
      }

      if (!this.isStarted()) {
        throw new Error('浏览器会话未启动，无法启用增强模式');
      }

      logger.info('🚀 启用增强模式...', 'BrowserSession');

      // 初始化主控制器
      this.masterController = new MasterController(this);
      await this.masterController.initialize();

      this.enhancedMode = true;
      logger.success('✅ 增强模式已启用', 'BrowserSession');

    } catch (error: any) {
      logger.error(`❌ 启用增强模式失败: ${error.message}`, error, 'BrowserSession');
      throw error;
    }
  }

  /**
   * 禁用增强模式
   */
  async disableEnhancedMode(): Promise<void> {
    try {
      if (!this.enhancedMode) {
        logger.warn('⚠️ 增强模式未启用', 'BrowserSession');
        return;
      }

      logger.info('🛑 禁用增强模式...', 'BrowserSession');

      if (this.masterController) {
        await this.masterController.shutdown();
        this.masterController = null;
      }

      this.enhancedMode = false;
      logger.success('✅ 增强模式已禁用', 'BrowserSession');

    } catch (error: any) {
      logger.error(`❌ 禁用增强模式失败: ${error.message}`, error, 'BrowserSession');
    }
  }

  /**
   * 检查是否启用了增强模式
   */
  isEnhancedModeEnabled(): boolean {
    return this.enhancedMode;
  }

  /**
   * 增强的DOM状态获取 - 使用智能缓存和检测
   */
  async getEnhancedDOMState(forceRefresh: boolean = false): Promise<DOMState> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      // 如果启用了增强模式，使用增强的DOM检测器
      if (this.enhancedMode && this.masterController) {
        logger.info('🚀 使用增强DOM检测器', 'BrowserSession');
        return await this.masterController.detectElements(forceRefresh);
      }

      // 回退到快速DOM检测（参考1.js性能优化）
      logger.info('⚡ 使用快速DOM检测', 'BrowserSession');
      return await this.getFastDOMState();

    } catch (error) {
      logger.error('Failed to get enhanced DOM state', error as Error, 'BrowserSession');
      throw error;
    }
  }

  /**
   * 快速DOM状态获取 - 参考1.js性能优化
   */
  async getFastDOMState(): Promise<DOMState> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      const startTime = Date.now();

      // 使用优化的DOM检测
      const domState = await this.domService!.getOptimizedDOMState();
      const elements = domState.elements;

      // 获取页面基本信息
      const [url, title] = await Promise.all([
        this.page.url(),
        this.page.title()
      ]);

      const endTime = Date.now();
      logger.info(`⚡ 快速DOM状态获取完成: ${elements.length}个元素, 耗时: ${endTime - startTime}ms`, 'BrowserSession');

      return {
        elements,
        url,
        title,
        screenshot: '' // 快速模式下跳过截图
      };

    } catch (error) {
      logger.error('快速DOM状态获取失败', error as Error, 'BrowserSession');
      // 最终回退到标准DOM服务
      return await this.domService!.getDOMState();
    }
  }

  /**
   * 智能标签页切换
   */
  async smartSwitchTab(criteria: {
    preferredDomain?: string;
    preferredPageType?: string;
    mustHaveElements?: string[];
    avoidErrors?: boolean;
    preferRecent?: boolean;
  } = {}): Promise<any> {
    if (!this.enhancedMode || !this.masterController) {
      throw new Error('增强模式未启用，无法使用智能标签页切换');
    }

    try {
      logger.info('🎯 执行智能标签页切换...', 'BrowserSession');
      const newPage = await this.masterController.smartSwitchTab(criteria);

      if (newPage) {
        // 更新当前页面引用
        this.page = newPage;

        // 更新DOM服务
        if (this.domService) {
          this.domService = new DOMService(this.page);
        }

        logger.success('✅ 智能标签页切换成功', 'BrowserSession');
        return newPage;
      } else {
        logger.warn('⚠️ 未找到合适的标签页', 'BrowserSession');
        return null;
      }

    } catch (error) {
      logger.error('Failed to smart switch tab', error as Error, 'BrowserSession');
      throw error;
    }
  }

  /**
   * 获取增强模式统计信息
   */
  getEnhancedModeStats(): any {
    if (!this.enhancedMode || !this.masterController) {
      return null;
    }

    return this.masterController.getOperationStats();
  }

  // Advanced browser features
  async hover(index: number, xpath?: string): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      if (xpath) {
        const element = this.page.locator(`xpath=${xpath}`);
        await element.hover();
        logger.info(`Hovered over element with xpath: ${xpath}`, 'BrowserSession');
      } else {
        const elements = await this.page.locator('[data-browser-use-index]').all();
        if (index >= 0 && index < elements.length) {
          await elements[index].hover();
          logger.info(`Hovered over element at index: ${index}`, 'BrowserSession');
        } else {
          throw new Error(`Element index ${index} out of range`);
        }
      }
      await this.page.waitForTimeout(300);
    } catch (error) {
      logger.error(`Failed to hover over element`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async dragAndDrop(sourceIndex: number, targetIndex: number, sourceXpath?: string, targetXpath?: string): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      let sourceElement, targetElement;

      if (sourceXpath && targetXpath) {
        sourceElement = this.page.locator(`xpath=${sourceXpath}`);
        targetElement = this.page.locator(`xpath=${targetXpath}`);
      } else {
        const elements = await this.page.locator('[data-browser-use-index]').all();
        if (sourceIndex >= elements.length || targetIndex >= elements.length) {
          throw new Error('Element index out of range');
        }
        sourceElement = elements[sourceIndex];
        targetElement = elements[targetIndex];
      }

      await sourceElement.dragTo(targetElement);
      logger.info(`Dragged element from ${sourceIndex} to ${targetIndex}`, 'BrowserSession');
      await this.page.waitForTimeout(500);
    } catch (error) {
      logger.error(`Failed to drag and drop`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async pressKey(key: string, modifiers?: string[], options: {
    waitForNavigation?: boolean;
    expectFormSubmit?: boolean;
    targetElement?: any;
    retryCount?: number;
  } = {}): Promise<boolean> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      logger.info(`⌨️ 按键操作: ${key}`, 'BrowserSession');

      // Store current state to detect navigation
      const currentUrl = this.page.url();
      const currentDOMHash = await this.getDOMStructureHash();

      if (modifiers && modifiers.length > 0) {
        const modifierString = modifiers.join('+') + '+' + key;
        await this.page.keyboard.press(modifierString);
        logger.info(`Pressed key combination: ${modifierString}`, 'BrowserSession');
      } else {
        await this.page.keyboard.press(key);
        logger.info(`Pressed key: ${key}`, 'BrowserSession');
      }

      // Wait for potential navigation after key press (e.g., Enter key)
      const navigationDetected = await this.waitForPotentialNavigation(currentUrl, currentDOMHash);

      if (navigationDetected) {
        logger.info('🔄 Navigation detected after key press, page state may have changed', 'BrowserSession');
      }

      return navigationDetected;
    } catch (error) {
      logger.error(`Failed to press key`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  /**
   * 执行强健的按键操作 - 使用Playwright最佳实践
   */
  private async performRobustKeyPress(key: string, modifiers?: string[], options: {
    waitForNavigation?: boolean;
    expectFormSubmit?: boolean;
    targetElement?: any;
    retryCount?: number;
  } = {}): Promise<boolean> {
    logger.info(`⌨️ 执行强健按键操作: ${key}`, 'BrowserSession');

    try {
      // Store current state to detect navigation
      const currentUrl = this.page.url();
      const currentDOMHash = await this.getDOMStructureHash();

      // 如果指定了目标元素，先聚焦到该元素
      if (options.targetElement) {
        await options.targetElement.focus();
        await this.page.waitForTimeout(100); // 等待聚焦完成
      }

      // 构建按键组合
      let keyCombo = key;
      if (modifiers && modifiers.length > 0) {
        keyCombo = modifiers.join('+') + '+' + key;
      }

      // 执行按键操作
      if (options.targetElement) {
        // 在特定元素上按键
        await options.targetElement.press(keyCombo);
      } else {
        // 在页面级别按键
        await this.page.keyboard.press(keyCombo);
      }

      logger.info(`✅ 按键操作成功: ${keyCombo}`, 'BrowserSession');

      // 等待潜在的导航或页面变化
      let navigationDetected = false;

      if (options.waitForNavigation || options.expectFormSubmit || key === 'Enter') {
        // 对于可能触发导航的按键，等待更长时间
        await this.page.waitForTimeout(500);
        navigationDetected = await this.waitForPotentialNavigation(currentUrl, currentDOMHash);
      } else {
        // 对于普通按键，等待较短时间
        await this.page.waitForTimeout(100);
        navigationDetected = await this.waitForPotentialNavigation(currentUrl, currentDOMHash);
      }

      if (navigationDetected) {
        logger.info('🔄 Navigation detected after key press, page state may have changed', 'BrowserSession');
      }

      return navigationDetected;

    } catch (error) {
      logger.error(`❌ 强健按键操作失败: ${key}`, error as Error, 'BrowserSession');

      // 尝试重试
      const retryCount = options.retryCount || 1;
      if (retryCount > 0) {
        logger.info(`🔄 重试按键操作: ${key}, 剩余重试次数: ${retryCount}`, 'BrowserSession');
        await this.page.waitForTimeout(200);
        return await this.performRobustKeyPress(key, modifiers, { ...options, retryCount: retryCount - 1 });
      }

      throw error;
    }
  }

  async selectOption(index: number, value: string | string[], xpath?: string): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      if (xpath) {
        const element = this.page.locator(`xpath=${xpath}`);
        await element.selectOption(value);
        logger.info(`Selected option in element with xpath: ${xpath}`, 'BrowserSession');
      } else {
        const elements = await this.page.locator('[data-browser-use-index]').all();
        if (index >= 0 && index < elements.length) {
          await elements[index].selectOption(value);
          logger.info(`Selected option in element at index: ${index}`, 'BrowserSession');
        } else {
          throw new Error(`Element index ${index} out of range`);
        }
      }
      await this.page.waitForTimeout(500);
    } catch (error) {
      logger.error(`Failed to select option`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async uploadFile(index: number, filePath: string, xpath?: string): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      if (xpath) {
        const element = this.page.locator(`xpath=${xpath}`);
        await element.setInputFiles(filePath);
        logger.info(`Uploaded file to element with xpath: ${xpath}`, 'BrowserSession');
      } else {
        const elements = await this.page.locator('[data-browser-use-index]').all();
        if (index >= 0 && index < elements.length) {
          await elements[index].setInputFiles(filePath);
          logger.info(`Uploaded file to element at index: ${index}`, 'BrowserSession');
        } else {
          throw new Error(`Element index ${index} out of range`);
        }
      }
      await this.page.waitForTimeout(1000);
    } catch (error) {
      logger.error(`Failed to upload file`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async executeScript(script: string, args?: any[]): Promise<any> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      const result = await this.page.evaluate(script, args);
      logger.info(`Executed script successfully`, 'BrowserSession');
      return result;
    } catch (error) {
      logger.error(`Failed to execute script`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async newTab(url?: string): Promise<number> {
    if (!this.context) {
      throw new Error('Browser session not started');
    }

    try {
      const newPage = await this.context.newPage();
      this.tabs.push(newPage);
      const tabIndex = this.tabs.length - 1;

      if (url) {
        await newPage.goto(url, { waitUntil: 'domcontentloaded' });
      }

      logger.info(`Created new tab (index: ${tabIndex})${url ? ` and navigated to ${url}` : ''}`, 'BrowserSession');
      return tabIndex;
    } catch (error) {
      logger.error(`Failed to create new tab`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async switchTab(tabIndex: number): Promise<void> {
    if (tabIndex < 0 || tabIndex >= this.tabs.length) {
      throw new Error(`Tab index ${tabIndex} out of range`);
    }

    try {
      this.currentTabIndex = tabIndex;
      this.page = this.tabs[tabIndex];

      if (this.domService) {
        this.domService = new DOMService(this.page);
      }

      await this.page.bringToFront();
      logger.info(`Switched to tab ${tabIndex}`, 'BrowserSession');
    } catch (error) {
      logger.error(`Failed to switch tab`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async closeTab(tabIndex?: number): Promise<void> {
    const indexToClose = tabIndex ?? this.currentTabIndex;

    if (indexToClose < 0 || indexToClose >= this.tabs.length) {
      throw new Error(`Tab index ${indexToClose} out of range`);
    }

    if (this.tabs.length === 1) {
      throw new Error('Cannot close the last tab');
    }

    try {
      await this.tabs[indexToClose].close();
      this.tabs.splice(indexToClose, 1);

      // Adjust current tab index if necessary
      if (this.currentTabIndex >= indexToClose && this.currentTabIndex > 0) {
        this.currentTabIndex--;
      }

      // Switch to the current tab
      this.page = this.tabs[this.currentTabIndex];
      if (this.domService) {
        this.domService = new DOMService(this.page);
      }

      logger.info(`Closed tab ${indexToClose}`, 'BrowserSession');
    } catch (error) {
      logger.error(`Failed to close tab`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async goBack(): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      await this.page.goBack({ waitUntil: 'domcontentloaded' });
      logger.info('Navigated back', 'BrowserSession');
      await this.page.waitForTimeout(1000);
    } catch (error) {
      logger.error('Failed to go back', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async goForward(): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      await this.page.goForward({ waitUntil: 'domcontentloaded' });
      logger.info('Navigated forward', 'BrowserSession');
      await this.page.waitForTimeout(1000);
    } catch (error) {
      logger.error('Failed to go forward', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async refresh(): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      await this.page.reload({ waitUntil: 'domcontentloaded' });
      logger.info('Page refreshed', 'BrowserSession');
      await this.page.waitForTimeout(1000);
    } catch (error) {
      logger.error('Failed to refresh page', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async setCookie(name: string, value: string, options?: {
    domain?: string;
    path?: string;
    expires?: number;
    httpOnly?: boolean;
    secure?: boolean;
    sameSite?: 'Strict' | 'Lax' | 'None';
  }): Promise<void> {
    if (!this.context) {
      throw new Error('Browser session not started');
    }

    try {
      const cookieData: any = {
        name,
        value,
        domain: options?.domain || new URL(this.getCurrentUrl()).hostname,
        path: options?.path || '/',
      };

      if (options?.expires !== undefined) cookieData.expires = options.expires;
      if (options?.httpOnly !== undefined) cookieData.httpOnly = options.httpOnly;
      if (options?.secure !== undefined) cookieData.secure = options.secure;
      if (options?.sameSite) cookieData.sameSite = options.sameSite;

      await this.context.addCookies([cookieData]);
      logger.info(`Set cookie: ${name}`, 'BrowserSession');
    } catch (error) {
      logger.error('Failed to set cookie', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async getCookies(): Promise<any[]> {
    if (!this.context) {
      throw new Error('Browser session not started');
    }

    try {
      return await this.context.cookies();
    } catch (error) {
      logger.error('Failed to get cookies', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async waitForElement(selector: string, timeout: number = 30000, state: 'visible' | 'hidden' | 'attached' | 'detached' = 'visible'): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      await this.page.waitForSelector(selector, { timeout, state });
      logger.info(`Element found: ${selector}`, 'BrowserSession');
    } catch (error) {
      logger.error(`Failed to wait for element: ${selector}`, error as Error, 'BrowserSession');
      throw error;
    }
  }

  async waitForNavigation(timeout: number = 30000, waitUntil: 'load' | 'domcontentloaded' | 'networkidle' = 'domcontentloaded'): Promise<void> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      await this.page.waitForLoadState(waitUntil, { timeout });
      logger.info('Navigation completed', 'BrowserSession');
    } catch (error) {
      logger.error('Failed to wait for navigation', error as Error, 'BrowserSession');
      throw error;
    }
  }

  async extractData(selector?: string, xpath?: string, attribute?: string, multiple: boolean = false): Promise<string | string[]> {
    if (!this.page) {
      throw new Error('Browser session not started');
    }

    try {
      let locator: any;
      if (xpath) {
        locator = this.page.locator(`xpath=${xpath}`);
      } else if (selector) {
        locator = this.page.locator(selector);
      } else {
        throw new Error('Either selector or xpath must be provided');
      }

      if (multiple) {
        const elements = await locator.all();
        const results: string[] = [];

        for (const element of elements) {
          if (attribute) {
            const value = await element.getAttribute(attribute);
            results.push(value || '');
          } else {
            const text = await element.textContent();
            results.push(text || '');
          }
        }

        logger.info(`Extracted ${results.length} values`, 'BrowserSession');
        return results;
      } else {
        let result: string;
        if (attribute) {
          result = await locator.getAttribute(attribute) || '';
        } else {
          result = await locator.textContent() || '';
        }

        logger.info(`Extracted data: ${result.substring(0, 50)}...`, 'BrowserSession');
        return result;
      }
    } catch (error) {
      logger.error('Failed to extract data', error as Error, 'BrowserSession');
      throw error;
    }
  }

  getTabCount(): number {
    return this.tabs.length;
  }

  getCurrentTabIndex(): number {
    return this.currentTabIndex;
  }

  private setupEventListeners(): void {
    if (!this.page) return;

    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        logger.debug(`Browser console error: ${msg.text()}`, 'BrowserSession');
      }
    });

    this.page.on('pageerror', error => {
      logger.debug(`Page error: ${error.message}`, 'BrowserSession');
    });

    this.page.on('requestfailed', request => {
      logger.debug(`Request failed: ${request.url()}`, 'BrowserSession');
    });
  }

  /**
   * Wait for potential navigation after an action (click, key press, etc.)
   * This method detects URL changes, DOM changes, and waits for page load completion
   * Similar to Python version's navigation detection logic
   */
  private async waitForPotentialNavigation(previousUrl: string, previousDOMHash?: string, timeout: number = 10000): Promise<boolean> {
    if (!this.page) return false;

    try {
      logger.info(`Checking for navigation from: ${previousUrl}`, 'BrowserSession');

      // Wait a short time to see if navigation starts
      await this.page.waitForTimeout(100);

      const currentUrl = this.page.url();
      logger.info(`Current URL after action: ${currentUrl}`, 'BrowserSession');

      // Check for URL change (traditional navigation)
      if (currentUrl !== previousUrl) {
        logger.info(`🔄 URL Navigation detected: ${previousUrl} → ${currentUrl}`, 'BrowserSession');

        try {
          // Wait for page to load completely
          await this.page.waitForLoadState('domcontentloaded', { timeout });
          logger.info('✅ URL Navigation completed successfully', 'BrowserSession');
          return true;
        } catch (error) {
          logger.warn(`⚠️ Navigation timeout or failed, continuing anyway: ${error}`, 'BrowserSession');
          return true; // Still consider it navigation even if timeout
        }
      }

      // Check for DOM structure change (SPA navigation)
      if (previousDOMHash) {
        try {
          // Wait a bit more for SPA content to load
          await this.page.waitForTimeout(1000);

          // Get current DOM hash to compare
          const currentDOMHash = await this.getDOMStructureHash();

          if (currentDOMHash !== previousDOMHash) {
            logger.info(`🔄 SPA Navigation detected: DOM structure changed`, 'BrowserSession');
            logger.info(`Previous DOM hash: ${previousDOMHash.substring(0, 10)}...`, 'BrowserSession');
            logger.info(`Current DOM hash: ${currentDOMHash.substring(0, 10)}...`, 'BrowserSession');

            // CRITICAL: Wait longer for SPA content to fully load
            logger.info('⏳ Waiting for SPA content to fully load...', 'BrowserSession');
            await this.page.waitForTimeout(2000);

            // Also wait for any network requests to complete
            try {
              await this.page.waitForLoadState('networkidle', { timeout: 5000 });
              logger.info('✅ Network idle achieved after SPA navigation', 'BrowserSession');
            } catch (networkError) {
              logger.warn(`⚠️ Network idle timeout, continuing anyway: ${networkError}`, 'BrowserSession');
            }

            return true;
          }
        } catch (error) {
          logger.warn(`Error checking DOM structure change: ${error}`, 'BrowserSession');
        }
      }

      logger.info('ℹ️ No navigation detected, waiting for dynamic content...', 'BrowserSession');
      // No navigation detected, just wait a bit for any dynamic content
      await this.page.waitForTimeout(500);
      return false;

    } catch (error) {
      logger.warn(`Error during navigation wait: ${error}`, 'BrowserSession');
      // Continue anyway, don't fail the entire action
      return false;
    }
  }

  /**
   * Get a hash of the current DOM structure to detect SPA navigation
   */
  private async getDOMStructureHash(): Promise<string> {
    try {
      const domStructure = await this.page.evaluate(() => {
        // Get a simplified representation of the DOM structure
        const getElementSignature = (element: Element): string => {
          const tag = element.tagName.toLowerCase();
          const id = element.id ? `#${element.id}` : '';
          const classes = element.className ? `.${element.className.split(' ').join('.')}` : '';
          const text = element.textContent?.trim().substring(0, 50) || '';
          return `${tag}${id}${classes}:${text}`;
        };

        // Get signatures of key elements that change during navigation
        const keyElements = document.querySelectorAll('main, [role="main"], .content, #content, .page, .container');
        const signatures = Array.from(keyElements).map(getElementSignature);

        // Also include page title and some meta info
        const title = document.title;
        const metaDescription = document.querySelector('meta[name="description"]')?.getAttribute('content') || '';

        return {
          title,
          metaDescription,
          keyElementSignatures: signatures,
          elementCount: document.querySelectorAll('*').length
        };
      });

      // Create a hash from the DOM structure
      const structureString = JSON.stringify(domStructure);
      return this.simpleHash(structureString);
    } catch (error) {
      logger.warn(`Error getting DOM structure hash: ${error}`, 'BrowserSession');
      return '';
    }
  }

  /**
   * Simple hash function for strings
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return hash.toString();
  }
}
