const { BrowserSession } = require('./dist/browser/session');
const { logger } = require('./dist/utils/logger');

async function testEnhancedFixes() {
  console.log('🧪 测试增强功能修复...\n');
  
  const browserSession = new BrowserSession({
    headless: false,
    viewport: { width: 1280, height: 720 }
  });

  try {
    // 步骤 1: 启动浏览器
    console.log('📋 步骤 1: 启动浏览器会话');
    await browserSession.start();
    console.log('✅ 浏览器会话已启动\n');

    // 步骤 2: 启用增强模式
    console.log('📋 步骤 2: 启用增强模式');
    await browserSession.enableEnhancedMode();
    console.log('✅ 增强模式已启用\n');

    // 步骤 3: 测试页面导航和状态监控
    console.log('📋 步骤 3: 测试页面导航和状态监控');
    await browserSession.navigate('https://www.bilibili.com');
    
    // 等待页面加载
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 获取DOM状态
    const domState1 = await browserSession.getEnhancedDOMState();
    console.log(`✅ 检测到 ${domState1.elements.length} 个元素`);
    console.log(`📍 当前页面: ${domState1.url}\n`);

    // 步骤 4: 测试搜索功能和按键处理
    console.log('📋 步骤 4: 测试搜索功能和按键处理');
    
    // 查找搜索框
    const searchElements = domState1.elements.filter(el =>
      (el.attributes.placeholder &&
       (el.attributes.placeholder.includes('搜索') || el.attributes.placeholder.includes('search'))) ||
      (el.attributes.class && el.attributes.class.includes('search')) ||
      (el.tagName === 'input' && el.attributes.type === 'search') ||
      (el.tagName === 'input' && !el.attributes.type && el.attributes.class && el.attributes.class.includes('nav-search'))
    );
    
    if (searchElements.length > 0) {
      const searchBox = searchElements[0];
      console.log(`🔍 找到搜索框: index ${searchBox.highlightIndex}`);
      
      // 点击搜索框
      await browserSession.click(searchBox.highlightIndex);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 输入搜索内容
      await browserSession.type(searchBox.highlightIndex, '测试视频');
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 测试增强的Enter键处理
      console.log('⌨️ 测试增强Enter键处理...');
      const navigationDetected = await browserSession.pressKey('Enter', [], {
        expectFormSubmit: true,
        waitForNavigation: true
      });
      
      console.log(`✅ Enter键处理完成，导航检测: ${navigationDetected}`);
      
      // 等待搜索结果加载
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 获取新的DOM状态
      const domState2 = await browserSession.getEnhancedDOMState();
      console.log(`✅ 搜索后检测到 ${domState2.elements.length} 个元素`);
      console.log(`📍 新页面: ${domState2.url}\n`);
      
    } else {
      console.log('⚠️ 未找到搜索框，跳过搜索测试\n');
    }

    // 步骤 5: 测试智能标签页管理
    console.log('📋 步骤 5: 测试智能标签页管理');
    
    // 创建新标签页
    await browserSession.createNewTab('https://github.com');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 智能切换标签页
    const switchResult = await browserSession.smartSwitchTab({
      preferredDomain: 'bilibili.com',
      avoidErrors: true
    });
    
    console.log(`✅ 智能标签页切换: ${switchResult ? '成功' : '失败'}\n`);

    // 步骤 6: 测试页面状态监控
    console.log('📋 步骤 6: 测试页面状态监控');
    
    // 导航到新页面触发状态监控
    await browserSession.navigate('https://www.npmjs.com');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const domState3 = await browserSession.getEnhancedDOMState();
    console.log(`✅ 新页面检测到 ${domState3.elements.length} 个元素`);
    console.log(`📍 最终页面: ${domState3.url}\n`);

    console.log('🎉 所有测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error(error.stack);
  } finally {
    // 清理资源
    console.log('\n📋 清理资源...');
    await browserSession.close();
    console.log('✅ 浏览器会话已关闭');
  }
}

// 运行测试
testEnhancedFixes().catch(console.error);
