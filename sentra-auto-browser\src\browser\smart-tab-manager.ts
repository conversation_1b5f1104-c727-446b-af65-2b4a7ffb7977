import { logger } from '../utils/logger';

/**
 * 🗂️ 智能标签页管理器 - 参考1.js全面重构版
 * 实时监控、智能评分、自动切换到最佳标签页
 */
export class SmartTabManager {
  private browser: any;
  private context: any;
  private tabs: Map<any, any> = new Map(); // 使用page对象作为key
  private activeTab: any = null;
  private lastUpdateTime: number = 0;
  private updateInterval: any = null;
  private monitoringActive: boolean = false;

  constructor(browser: any, context: any) {
    this.browser = browser;
    this.context = context;
  }

  /**
   * 启动标签页监控 - 参考1.js实现
   */
  async startMonitoring() {
    if (this.monitoringActive) return;

    logger.info('🗂️ 启动智能标签页管理器...', 'SmartTabManager');

    // 监听新页面创建 - 立即处理
    this.context.on('page', (page: any) => {
      logger.info('📄 检测到新页面创建', 'SmartTabManager');
      this.handleNewPage(page);
    });

    // 定期更新标签页信息 - 更频繁的更新
    this.updateInterval = setInterval(() => {
      this.updateAllTabs().catch(console.error);
    }, 2000); // 从3000ms减少到2000ms

    // 初始扫描
    await this.updateAllTabs();

    this.monitoringActive = true;
    logger.info('✅ 智能标签页管理器已启动', 'SmartTabManager');
  }

  /**
   * 处理新页面创建 - 参考1.js智能处理
   */
  async handleNewPage(page: any) {
    try {
      // 等待页面基本加载
      await page.waitForLoadState('domcontentloaded', { timeout: 5000 });

      const tabInfo = await this.createTabInfo(page);
      this.tabs.set(page, tabInfo);

      logger.info(`📄 新标签页已注册: ${tabInfo.title}`, 'SmartTabManager');

      // 智能切换逻辑 - 立即评估是否切换
      await this.smartSwitchTab();

    } catch (error: any) {
      logger.warn(`⚠️ 处理新页面失败: ${error.message}`, 'SmartTabManager');
    }
  }

  /**
   * 更新所有标签页信息 - 参考1.js实现
   */
  async updateAllTabs() {
    try {
      const currentPages = this.context.pages();

      // 清理已关闭的页面
      for (const [page] of this.tabs) {
        if (!currentPages.includes(page)) {
          logger.info('🗑️ 清理已关闭的标签页', 'SmartTabManager');
          this.tabs.delete(page);
        }
      }

      // 更新现有页面信息
      for (const page of currentPages) {
        try {
          const tabInfo = await this.createTabInfo(page);
          this.tabs.set(page, tabInfo);
        } catch (error: any) {
          logger.warn(`⚠️ 更新标签页信息失败: ${error.message}`, 'SmartTabManager');
        }
      }

      // 智能选择活动标签页
      await this.smartSwitchTab();

      this.lastUpdateTime = Date.now();

    } catch (error: any) {
      logger.error(`❌ 更新标签页信息失败: ${error.message}`, error, 'SmartTabManager');
    }
  }

  /**
   * 创建标签页信息 - 参考1.js增强版
   */
  private async createTabInfo(page: any): Promise<any> {
    try {
      // 安全获取页面信息
      let url = 'unknown';
      let title = 'Unknown';

      try {
        url = await page.url();
        title = await page.title();
      } catch (error: any) {
        logger.warn(`获取页面基本信息失败: ${error.message}`, 'SmartTabManager');
      }

      // 获取页面详细信息 - 参考1.js
      const pageInfo = await page.evaluate(() => {
        return {
          readyState: document.readyState,
          elementCount: document.querySelectorAll('*').length,
          interactiveCount: document.querySelectorAll(
            'button, input, a, select, textarea, [onclick], [role="button"]'
          ).length,
          hasContent: document.body ? document.body.innerText.trim().length > 100 : false,
          timestamp: Date.now()
        };
      }).catch(() => ({
        readyState: 'loading',
        elementCount: 0,
        interactiveCount: 0,
        hasContent: false,
        timestamp: Date.now()
      }));

      return {
        page,
        url,
        title,
        ...pageInfo,
        lastUpdate: Date.now(),
        score: this.calculateTabScore(url, title, pageInfo)
      };
    } catch (error: any) {
      logger.warn(`⚠️ 创建标签页信息失败: ${error.message}`, 'SmartTabManager');
      return {
        page,
        url: 'unknown',
        title: 'Unknown',
        readyState: 'loading',
        elementCount: 0,
        interactiveCount: 0,
        hasContent: false,
        lastUpdate: Date.now(),
        score: 0
      };
    }
  }

  /**
   * 计算标签页评分 - 参考1.js智能评分系统
   */
  private calculateTabScore(url: string, title: string, pageInfo: any): number {
    let score = 0;

    // 页面加载状态得分
    if (pageInfo.readyState === 'complete') score += 30;
    else if (pageInfo.readyState === 'interactive') score += 15;

    // 内容丰富度得分
    if (pageInfo.hasContent) score += 20;
    score += Math.min(pageInfo.interactiveCount * 2, 30);
    score += Math.min(pageInfo.elementCount / 50, 20);

    // URL优先级得分（电商、搜索结果等）
    const priorityDomains = [
      'taobao.com', 'tmall.com', 'jd.com', 'amazon',
      'search', 'results', 'item', 'product', 'detail'
    ];

    for (const domain of priorityDomains) {
      if (url.toLowerCase().includes(domain)) {
        score += 25;
        break;
      }
    }

    // 最近更新得分
    const timeSinceUpdate = Date.now() - pageInfo.timestamp;
    score += Math.max(0, 20 - (timeSinceUpdate / 1000));

    return Math.round(score);
  }

  /**
   * 分析标签页内容
   */
  async analyzeTab(page: any) {
    try {
      // 检查页面是否还存在且可访问
      if (!page || page.isClosed()) {
        return {
          url: 'unknown',
          title: 'Unknown',
          domain: 'unknown',
          pageType: 'unknown',
          hasErrors: true,
          contentLength: 0,
          interactiveElements: 0
        };
      }

      const analysis = await page.evaluate(() => {
        try {
        // 获取页面基本信息
        const getPageInfo = () => {
          return {
            url: window.location.href,
            title: document.title,
            domain: window.location.hostname,
            path: window.location.pathname,
            isLoading: document.readyState !== 'complete'
          };
        };

        // 分析页面内容
        const analyzeContent = () => {
          const forms = document.querySelectorAll('form');
          const inputs = document.querySelectorAll('input, textarea, select');
          const buttons = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
          const links = document.querySelectorAll('a[href]');
          const images = document.querySelectorAll('img');
          
          // 检测页面类型
          let pageType = 'unknown';
          if (forms.length > 0) pageType = 'form';
          else if (links.length > 20) pageType = 'navigation';
          else if (images.length > 10) pageType = 'media';
          else if (document.querySelector('table')) pageType = 'data';
          
          // 获取可见文本
          const getVisibleText = () => {
            try {
              if (!document.body) return '';

              const walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                {
                  acceptNode: (node: any) => {
                    try {
                      const parent = node.parentElement;
                      if (!parent) return NodeFilter.FILTER_REJECT;

                      const style = window.getComputedStyle(parent);
                      if (style.display === 'none' || style.visibility === 'hidden') {
                        return NodeFilter.FILTER_REJECT;
                      }

                      return node.textContent.trim() ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;
                    } catch (nodeError) {
                      return NodeFilter.FILTER_REJECT;
                    }
                  }
                }
              );

              let text = '';
              let node;
              while (node = walker.nextNode()) {
                text += node.textContent.trim() + ' ';
              }
              return text.substring(0, 1000); // 限制长度
            } catch (textError) {
              return '';
            }
          };

          return {
            pageType,
            elementCounts: {
              forms: forms.length,
              inputs: inputs.length,
              buttons: buttons.length,
              links: links.length,
              images: images.length
            },
            visibleText: getVisibleText(),
            hasErrors: !!document.querySelector('.error, .alert-danger, [class*="error"]'),
            hasSuccess: !!document.querySelector('.success, .alert-success, [class*="success"]')
          };
        };

          return {
            ...getPageInfo(),
            ...analyzeContent()
          };
        } catch (evalError) {
          // 如果页面执行失败，返回基本信息
          return {
            url: window.location?.href || 'unknown',
            title: document?.title || 'Unknown',
            domain: window.location?.hostname || 'unknown',
            path: window.location?.pathname || '/',
            isLoading: true,
            pageType: 'unknown',
            elementCounts: { forms: 0, inputs: 0, buttons: 0, links: 0, images: 0 },
            visibleText: '',
            hasErrors: true,
            hasSuccess: false
          };
        }
      }).catch(() => {
        // 如果evaluate完全失败，返回默认值
        return {
          url: 'unknown',
          title: 'Unknown',
          domain: 'unknown',
          path: '/',
          isLoading: true,
          pageType: 'unknown',
          elementCounts: { forms: 0, inputs: 0, buttons: 0, links: 0, images: 0 },
          visibleText: '',
          hasErrors: true,
          hasSuccess: false
        };
      });

      return analysis;

    } catch (error: any) {
      logger.warn(`⚠️ 分析标签页失败: ${error.message}`, 'SmartTabManager');
      return {
        url: 'unknown',
        title: 'Unknown',
        domain: 'unknown',
        path: '/',
        isLoading: false,
        pageType: 'unknown',
        elementCounts: { forms: 0, inputs: 0, buttons: 0, links: 0, images: 0 },
        visibleText: '',
        hasErrors: false,
        hasSuccess: false
      };
    }
  }

  /**
   * 智能切换标签页 - 参考1.js实现
   */
  async smartSwitchTab(criteria: any = {}) {
    try {
      if (this.tabs.size === 0) return null;

      let bestTab = null;
      let bestScore = -1;

      for (const tabInfo of this.tabs.values()) {
        if (tabInfo.score > bestScore) {
          bestScore = tabInfo.score;
          bestTab = tabInfo;
        }
      }

      if (bestTab && bestTab.page !== this.activeTab) {
        logger.info(`🔄 智能切换到最佳标签页: ${bestTab.title} (评分: ${bestScore})`, 'SmartTabManager');

        this.activeTab = bestTab.page;

        // 确保页面可见
        await this.activeTab.bringToFront();
        await this.sleep(500);

        return this.activeTab;
      }

      return this.activeTab;

    } catch (error: any) {
      logger.error(`❌ 智能切换标签页失败: ${error.message}`, error, 'SmartTabManager');
      return this.activeTab;
    }
  }

  private async sleep(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }



  /**
   * 生成标签页ID
   */
  generateTabId(page: any) {
    return `tab_${page._guid || Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 清理已关闭的标签页
   */
  cleanupClosedTabs(activePagesArray: any[]) {
    const activePageGuids = new Set(activePagesArray.map(p => p._guid));
    
    for (const [tabId, tab] of this.tabs.entries()) {
      if (!activePageGuids.has(tab.page._guid)) {
        this.tabs.delete(tabId);
        logger.info(`🗑️ 清理已关闭的标签页: ${tabId}`, 'SmartTabManager');
      }
    }
  }

  /**
   * 获取所有标签页信息
   */
  getAllTabs() {
    return Array.from(this.tabs.values());
  }

  /**
   * 获取当前活动标签页
   */
  getActiveTab() {
    return this.activeTab;
  }

  /**
   * 获取当前活动页面对象
   */
  getActiveTabPage() {
    return this.activeTab ? this.activeTab.page : null;
  }

  /**
   * 停止监控
   */
  stopMonitoring() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
    this.monitoringActive = false;
    logger.info('🛑 智能标签页管理器已停止', 'SmartTabManager');
  }
}
