const { BrowserSession } = require('./dist/browser/session');

async function testSearchFix() {
  console.log('🧪 测试搜索功能修复...\n');
  
  const browserSession = new BrowserSession({
    headless: false,
    viewport: { width: 1280, height: 720 }
  });

  try {
    // 启动浏览器并启用增强模式
    await browserSession.start();
    await browserSession.enableEnhancedMode();
    console.log('✅ 浏览器和增强模式已启动\n');

    // 导航到B站
    await browserSession.navigate('https://www.bilibili.com');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 获取DOM状态
    const domState = await browserSession.getEnhancedDOMState();
    console.log(`✅ 检测到 ${domState.elements.length} 个元素`);
    console.log(`📍 当前页面: ${domState.url}\n`);

    // 查找搜索框
    console.log('🔍 调试元素信息:');
    console.log(`总元素数: ${domState.elements.length}`);

    // 查找所有input元素
    const allInputs = domState.elements.filter(el => el.tag === 'input');
    console.log(`所有input元素: ${allInputs.length}个`);
    allInputs.forEach((input, i) => {
      console.log(`  ${i+1}. index: ${input.highlightIndex}, placeholder: "${input.attributes.placeholder || 'none'}", class: "${input.attributes.class || 'none'}", type: "${input.attributes.type || 'none'}"`);
    });

    // 查找搜索相关元素
    const searchElements = domState.elements.filter(el =>
      (el.tag === 'input' &&
       el.attributes.placeholder &&
       el.attributes.placeholder.includes('搜索')) ||
      (el.attributes.class && el.attributes.class.includes('search'))
    );

    console.log(`🔍 找到 ${searchElements.length} 个搜索相关元素`);
    
    if (searchElements.length > 0) {
      const searchBox = searchElements[0];
      console.log(`🎯 使用搜索框: index ${searchBox.highlightIndex}`);
      
      // 点击搜索框
      await browserSession.click(searchBox.highlightIndex);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 输入搜索内容
      await browserSession.type(searchBox.highlightIndex, '搞笑视频');
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // 使用增强Enter键
      console.log('🚀 使用增强Enter键...');
      const result = await browserSession.pressKey('Enter', [], {
        waitForNavigation: true,
        expectFormSubmit: true  // B站搜索应该提交搜索
      });
      
      console.log(`✅ Enter键结果: ${result}`);
      
      // 等待并检查结果
      await new Promise(resolve => setTimeout(resolve, 3000));
      const newState = await browserSession.getEnhancedDOMState();
      console.log(`📍 新页面: ${newState.url}`);
      
      if (newState.url !== domState.url) {
        console.log('🎉 页面导航成功！');
      } else {
        console.log('⚠️ 页面未跳转，检查内容变化...');
      }
      
    } else {
      console.log('❌ 未找到搜索框');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await browserSession.close();
    console.log('✅ 测试完成');
  }
}

testSearchFix().catch(console.error);
