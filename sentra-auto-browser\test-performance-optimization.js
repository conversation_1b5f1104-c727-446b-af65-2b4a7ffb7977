const { BrowserSession } = require('./dist/browser/session');

async function testPerformanceOptimizations() {
  console.log('🚀 测试性能优化效果...\n');

  const browserProfile = {
    headless: false,
    viewport: { width: 1280, height: 720 },
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  };

  const browserSession = new BrowserSession(browserProfile);

  try {
    // 启动浏览器
    console.log('📱 启动浏览器...');
    const startTime = Date.now();
    await browserSession.start();
    console.log(`✅ 浏览器启动完成 (${Date.now() - startTime}ms)\n`);

    // 启用增强模式
    console.log('⚡ 启用增强模式...');
    const enhancedStartTime = Date.now();
    await browserSession.enableEnhancedMode();
    console.log(`✅ 增强模式启用完成 (${Date.now() - enhancedStartTime}ms)\n`);

    // 导航到百度
    console.log('🌐 导航到百度...');
    const navStartTime = Date.now();
    await browserSession.navigate('https://www.baidu.com');
    console.log(`✅ 页面加载完成 (${Date.now() - navStartTime}ms)\n`);

    // 快速DOM检测
    console.log('🔍 执行DOM检测...');
    const domStartTime = Date.now();
    const domState = await browserSession.getEnhancedDOMState();
    console.log(`✅ DOM检测完成 (${Date.now() - domStartTime}ms)`);
    console.log(`📊 检测到 ${domState.elements.length} 个元素\n`);

    // 测试搜索框输入和Enter键
    const searchElements = domState.elements.filter(el => 
      el.attributes.id === 'kw' || 
      el.attributes.name === 'wd' ||
      (el.attributes.class && el.attributes.class.includes('s_ipt'))
    );

    if (searchElements.length > 0) {
      console.log(`🔍 找到搜索框，索引: ${searchElements[0].index}`);
      
      // 点击搜索框
      console.log('👆 点击搜索框...');
      const clickStartTime = Date.now();
      await browserSession.click(searchElements[0].index);
      console.log(`✅ 点击完成 (${Date.now() - clickStartTime}ms)\n`);
      
      // 输入搜索内容
      console.log('⌨️ 输入搜索内容...');
      const typeStartTime = Date.now();
      await browserSession.type(searchElements[0].index, '性能测试');
      console.log(`✅ 输入完成 (${Date.now() - typeStartTime}ms)\n`);
      
      // 使用优化的Enter键处理
      console.log('⚡ 使用优化的Enter键处理...');
      const enterStartTime = Date.now();
      const navigationDetected = await browserSession.pressKey('Enter', [], {
        waitForNavigation: true,
        expectFormSubmit: true
      });
      console.log(`✅ Enter键处理完成 (${Date.now() - enterStartTime}ms)`);
      
      if (navigationDetected) {
        console.log('🎯 Enter键触发了页面导航或标签页切换');
        
        // 快速重新检测DOM
        console.log('🔄 重新检测DOM...');
        const redetectStartTime = Date.now();
        const newDomState = await browserSession.getEnhancedDOMState(true);
        console.log(`✅ DOM重新检测完成 (${Date.now() - redetectStartTime}ms)`);
        console.log(`📊 新页面检测到 ${newDomState.elements.length} 个元素`);
      } else {
        console.log('ℹ️ Enter键未触发导航，可能是AJAX搜索');
      }
    } else {
      console.log('⚠️ 未找到搜索框，跳过搜索演示');
    }

    console.log('\n🎉 性能优化测试完成！');
    console.log('主要优化点：');
    console.log('- ✅ 减少等待时间（2000ms -> 500ms）');
    console.log('- ✅ 快速标签页切换（1000ms -> 300ms）');
    console.log('- ✅ 删除中文硬编码，泛化处理');
    console.log('- ✅ 点击后自动检测新标签页');
    console.log('- ✅ 立即更新页面引用，无延迟');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    console.log('\n🔚 关闭浏览器...');
    await browserSession.close();
    console.log('✅ 浏览器已关闭');
  }
}

// 运行测试
testPerformanceOptimizations().catch(console.error);
