const { BrowserSession } = require('./dist/browser/session');
const fs = require('fs');
const path = require('path');

async function debugBuildDomDetailed() {
  console.log('🔍 详细调试buildDomTree.js执行...');
  
  const session = new BrowserSession();
  
  try {
    await session.start();
    await session.navigate('https://www.baidu.com');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 加载buildDomTree.js脚本
    const scriptPath = path.join(__dirname, 'dist/dom/buildDomTree.js');
    const buildDomTreeScript = fs.readFileSync(scriptPath, 'utf8');
    
    console.log('📄 buildDomTree.js脚本长度:', buildDomTreeScript.length);
    
    // 测试脚本执行
    const result = await session.page.evaluate(`
      try {
        const args = {
          doHighlightElements: true,
          focusHighlightIndex: -1,
          viewportExpansion: 0,
          debugMode: true
        };

        console.log('执行buildDomTree函数，参数:', args);
        const result = (${buildDomTreeScript})(args);
        console.log('buildDomTree返回结果类型:', typeof result);
        console.log('buildDomTree返回结果键:', Object.keys(result || {}));
        
        if (result && result.map) {
          const mapKeys = Object.keys(result.map);
          console.log('DOM map包含', mapKeys.length, '个节点');
          
          // 查找有highlightIndex的节点
          let highlightedNodes = 0;
          for (const key of mapKeys) {
            const node = result.map[key];
            if (node && typeof node.highlightIndex === 'number') {
              highlightedNodes++;
              if (highlightedNodes <= 3) {
                console.log('高亮节点示例:', {
                  id: key,
                  highlightIndex: node.highlightIndex,
                  tagName: node.tagName,
                  text: (node.text || '').substring(0, 50)
                });
              }
            }
          }
          console.log('总共找到', highlightedNodes, '个高亮节点');
        }
        
        return {
          success: true,
          result: result,
          error: null
        };
      } catch (error) {
        console.error('buildDomTree执行错误:', error.message);
        return {
          success: false,
          result: null,
          error: error.message
        };
      }
    `);
    
    console.log('✅ 执行结果:');
    console.log('  - 成功:', result.success);
    if (result.error) {
      console.log('  - 错误:', result.error);
    }
    if (result.result) {
      console.log('  - 返回值类型:', typeof result.result);
      console.log('  - 返回值键:', Object.keys(result.result));
      if (result.result.map) {
        console.log('  - DOM map节点数:', Object.keys(result.result.map).length);
      }
    }
    
  } catch (error) {
    console.error('❌ 调试失败:', error.message);
    console.error(error.stack);
  } finally {
    await session.close();
  }
}

debugBuildDomDetailed().catch(console.error);
