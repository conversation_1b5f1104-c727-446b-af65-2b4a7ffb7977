#!/usr/bin/env node

/**
 * 测试强健元素交互系统
 * 验证新的Playwright最佳实践实现
 */

const { BrowserSession } = require('./dist/browser/session');
const { logger } = require('./dist/utils/logger');

async function testRobustInteraction() {
  logger.info('🚀 开始测试强健元素交互系统', 'TestRobustInteraction');
  
  const session = new BrowserSession();
  
  try {
    // 启动浏览器
    await session.start({
      headless: false,
      viewport: { width: 1280, height: 720 }
    });
    
    logger.info('✅ 浏览器启动成功', 'TestRobustInteraction');
    
    // 导航到测试页面
    await session.navigate('https://www.google.com');
    logger.info('✅ 导航到Google成功', 'TestRobustInteraction');
    
    // 测试快速DOM检测
    logger.info('🔍 测试快速DOM检测...', 'TestRobustInteraction');
    const startTime = Date.now();
    const domState = await session.getFastDOMState();
    const endTime = Date.now();
    
    logger.info(`⚡ 快速DOM检测完成: ${domState.elements.length}个元素, 耗时: ${endTime - startTime}ms`, 'TestRobustInteraction');
    
    // 显示前5个元素的信息
    domState.elements.slice(0, 5).forEach((element, index) => {
      logger.info(`元素${index}: ${element.tag} - "${element.text}" - index: ${element.index}`, 'TestRobustInteraction');
    });
    
    // 测试强健的文本输入
    logger.info('⌨️ 测试强健文本输入...', 'TestRobustInteraction');
    
    // 查找搜索框（通常是第一个input元素）
    const searchInput = domState.elements.find(el => el.tag === 'input' && el.attributes.type !== 'hidden');
    if (searchInput) {
      logger.info(`找到搜索框: index ${searchInput.index}`, 'TestRobustInteraction');
      
      // 测试输入文本
      await session.type(searchInput.index, 'Playwright最佳实践测试');
      logger.info('✅ 文本输入成功', 'TestRobustInteraction');
      
      // 等待一下
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 测试强健的按键操作
      logger.info('⌨️ 测试强健按键操作...', 'TestRobustInteraction');
      const navigationDetected = await session.pressKey('Enter', [], {
        waitForNavigation: true,
        expectFormSubmit: true
      });
      
      if (navigationDetected) {
        logger.info('✅ 检测到导航，按键操作成功', 'TestRobustInteraction');
        
        // 等待页面加载
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 再次测试快速DOM检测
        logger.info('🔍 测试导航后的快速DOM检测...', 'TestRobustInteraction');
        const newStartTime = Date.now();
        const newDomState = await session.getFastDOMState();
        const newEndTime = Date.now();
        
        logger.info(`⚡ 导航后快速DOM检测完成: ${newDomState.elements.length}个元素, 耗时: ${newEndTime - newStartTime}ms`, 'TestRobustInteraction');
        
        // 测试点击操作
        const clickableElement = newDomState.elements.find(el => el.isClickable && el.text && el.text.length > 0);
        if (clickableElement) {
          logger.info(`🎯 测试点击操作: ${clickableElement.tag} - "${clickableElement.text}"`, 'TestRobustInteraction');
          
          try {
            const clickNavigationDetected = await session.click(clickableElement.index);
            logger.info(`✅ 点击操作完成, 导航检测: ${clickNavigationDetected}`, 'TestRobustInteraction');
          } catch (error) {
            logger.warn(`⚠️ 点击操作失败: ${error.message}`, 'TestRobustInteraction');
          }
        }
        
      } else {
        logger.info('ℹ️ 未检测到导航，可能是页面内搜索', 'TestRobustInteraction');
      }
      
    } else {
      logger.warn('⚠️ 未找到搜索框', 'TestRobustInteraction');
    }
    
    logger.info('🎉 强健元素交互系统测试完成', 'TestRobustInteraction');
    
  } catch (error) {
    logger.error('❌ 测试失败', error, 'TestRobustInteraction');
  } finally {
    // 等待一下让用户看到结果
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // 关闭浏览器
    await session.close();
    logger.info('✅ 浏览器已关闭', 'TestRobustInteraction');
  }
}

// 运行测试
if (require.main === module) {
  testRobustInteraction().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = { testRobustInteraction };
